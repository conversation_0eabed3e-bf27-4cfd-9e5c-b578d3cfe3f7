<?php

namespace App\Models;

use App\Utils\AnnonceInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\View\View;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;
use Wildside\Userstamps\Userstamps;

class LocationMeublee extends Model implements AnnonceInterface
{
    use HasFactory, SoftDeletes, Userstamps;

    protected $fillable = [
        'nombre_chambre',
        'nombre_personne',
        'superficie',
        'prix_min',
        'prix_max',
        // 'type',
        'nombre_salles_bain',
    ];

    protected $casts = [
        'nombre_chambre' => PurifyHtmlOnGet::class,
        'nombre_personne' => PurifyHtmlOnGet::class,
        'superficie' => PurifyHtmlOnGet::class,
        'prix_min' => PurifyHtmlOnGet::class,
        'prix_max' => PurifyHtmlOnGet::class,
    ];

    protected $appends = [
        // common
        'show_url',
        // 'edit_url',
        'caracteristiques',
        // 'informations',
        // 'equipements',

        'types_lit',
        'commodites',
        'services',
        'equipements_hebergement',
        'equipements_salle_bain',
        'equipements_cuisine',
        'commodites',
        'types_hebergement',

        'public_edit_url',
    ];

    public function getShowUrlAttribute(): string
    {
        return route('location-meublees.show', $this);
    }

    // public function getEditUrlAttribute(): string
    // {
    //     return route('location-meublees.edit', $this);
    // }

    public function annonce(): MorphOne
    {
        return $this->morphOne(Annonce::class, 'annonceable');
    }

    public function getTypesLitAttribute()
    {
        return $this->annonce->references('types-de-lit');
    }

    public function getCommoditesAttribute()
    {
        return $this->annonce->references('commodites-hebergement');
    }

    public function getServicesAttribute()
    {
        return $this->annonce->references('services');
    }

    public function getEquipementsHebergementAttribute()
    {
        return $this->annonce->references('equipements-hebergement');
    }

    public function getEquipementsSalleBainAttribute()
    {
        return $this->annonce->references('equipements-salle-de-bain');
    }

    public function getEquipementsCuisineAttribute()
    {
        return $this->annonce->references('accessoires-de-cuisine');
    }

    public function getTypesHebergementAttribute()
    {
        return $this->annonce->references('types-hebergement');
    }

    public function getShowInformationHeader(): View
    {
        return view('components.public.show.default-information-header');
    }

    public function getShowInformationBody(): View
    {
        return view('components.public.show.default-information-body', [
            'annonce' => $this->annonce,
        ]);
    }

    public function getCaracteristiquesAttribute(): array
    {
        $attributes = [
            'Nombre de chambre' => $this->nombre_chambre,
            'Nombre de personne' => $this->nombre_personne,
            'Nombre de salle de bain' => $this->nombre_salles_bain,
            'Superficie' => $this->superficie,
            'Prix minimum' => $this->prix_min ? number_format($this->prix_min, 0, ',', ' ') : null,
            'Prix maximum' => $this->prix_max ? number_format($this->prix_max, 0, ',', ' ') : null,
        ];

        return array_filter($attributes, function ($value) {
            return ! is_null($value);
        });
    }

    public function getPublicEditUrlAttribute(): string
    {
        return route('public.furnished-rentals.edit', $this);
    }
}
