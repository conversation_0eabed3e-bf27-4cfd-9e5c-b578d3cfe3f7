INSERT INTO `references` (`id`, `type`, `slug_type`, `nom`, `slug_nom`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 'Hébergement', 'hebergement', 'Commodités hébergement', 'commodites-hebergement', '2024-11-20 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(2, 'Hébergement', 'hebergement', 'Services proposés', 'services-proposes', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(3, 'Hébergement', 'hebergement', 'Types de lit', 'types-de-lit', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(4, 'Hébergement', 'hebergement', 'Equipements hébergement', 'equipements-hebergement', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(5, 'Hébergement', 'hebergement', 'Equipements salle de bain', 'equipements-salle-de-bain', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(6, 'Hébergement', 'hebergement', 'Accessoires de cuisine', 'accessoires-de-cuisine', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(7, 'Hébergement', 'hebergement', 'Types hebergement', 'types-hebergement', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(8, 'Location de véhicule', 'location-de-vehicule', 'Type de voiture', 'types-de-voiture', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(9, 'Location de véhicule', 'location-de-vehicule', 'Options et accesoires', 'options-accessoires', '2024-11-22 07:43:32', '2024-11-22 07:43:32', NULL, 1, 1, NULL),
(10, 'Restauration', 'restauration', 'Boissons disponibles', 'boissons-disponibles', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(11, 'Location de véhicule', 'location-de-vehicule', 'Boite de vitesses', 'boite-de-vitesses', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(12, 'Location de véhicule', 'location-de-vehicule', 'Conditions de location', 'conditions-de-location', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(14, 'Restauration', 'restauration', 'Equipements restauration', 'equipements-restauration', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(15, 'Restauration', 'restauration', 'Equipements patisserie', 'equipements-patisserie', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(16, 'Restauration', 'restauration', 'Produits fast-food', 'produits-fast-food', '2024-11-22 07:43:33', '2025-02-20 06:39:22', '2025-02-20 06:39:22', 1, 1, NULL),
(17, 'Restauration', 'restauration', 'Produits patissiers', 'produits-patissiers', '2024-11-22 07:43:33', '2025-02-20 06:39:22', '2025-02-20 06:39:22', 1, 1, NULL),
(18, 'Location de véhicule', 'location-de-vehicule', 'Type de moteur', 'types-moteur', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(19, 'Vie nocturne', 'vie-nocturne', 'Types de musique', 'types-de-musique', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(20, 'Vie nocturne', 'vie-nocturne', 'Commodités vie nocturne', 'commodites-vie-nocturne', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(21, 'Vie nocturne', 'vie-nocturne', 'Equipements vie nocturne', 'equipements-vie-nocturne', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(22, 'Restauration', 'restauration', 'Types de cuisine', 'types-cuisine', '2024-11-22 07:43:33', '2024-11-22 07:43:33', NULL, 1, 1, NULL),
(23, 'Vie nocturne', 'vie-nocturne', 'Services', 'services', '2024-12-23 08:52:13', '2024-12-23 08:52:18', NULL, 1, 1, NULL),
(24, 'Restauration', 'restauration', 'Services proposés', 'services-proposes', '2024-12-23 09:06:05', '2024-12-23 09:06:07', NULL, 1, 1, NULL);

INSERT INTO `reference_valeurs` (`id`, `valeur`, `reference_id`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 'Piscine', 1, '2024-11-21 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(2, 'Salle de sport', 1, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(3, 'Balcon/terrasse', 1, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(4, 'Jardin ou barbecue', 1, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(5, 'Jeux et divertissements', 1, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(6, 'Nettoyage d\'entrée/sortie', 2, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(7, 'Conciergerie', 2, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(8, 'Gardiennage', 2, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(9, 'Petit déjeuner compris', 2, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(10, 'Lit simple', 3, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(11, 'Lit double', 3, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(12, 'Queen size', 3, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(13, 'Lit bébé', 3, '2024-11-22 07:59:53', '2024-12-11 08:06:38', '2024-12-11 08:06:38', 1, 1, 1),
(14, 'Lits superposés', 3, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(15, 'Placards, commodes, armoires', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(16, 'Système de sécurité (caméras, alarmes)', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(17, 'Connexion internet', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(18, 'Détecteurs de fumée et de monoxyde de carbone', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(19, 'Aspirateur ', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(20, 'Multiprises et chargeurs', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(21, 'Canapé', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(22, 'Lave-linge.', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(23, 'Sèche-linge (ou étendoir)', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(24, 'Table et fer à repasser', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(25, 'Kit de premiers secours', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(26, 'Jeux ou jouets', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(27, 'Climatisée', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(28, 'Ventilée', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(29, 'Téléviseur', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(30, 'Imprimante', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(31, 'Ordinateur', 4, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(32, 'Lavabo ou Vasque', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(33, 'Cabine de douche', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(34, 'Baignoire', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(35, 'WC intégré salle de bain', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(36, 'WC séparé', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(37, 'Panier à linge', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(38, 'Armoire de toilette', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(39, 'Colonnes de rangement', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(40, 'Eau chaude', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(41, 'Tapis de bain', 5, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(42, 'Four', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(43, 'Plaques de cuissons', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(44, 'Plan de travail', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(45, 'Réfrigérateur', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(46, 'Lave-vaisselle', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(47, 'Hotte aspirante', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(48, 'Poubelle intégrée', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(49, 'Micro-ondes', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(50, 'Bouilloire', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(51, 'Blendeur/mixeur', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(52, 'Machine à café', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(53, 'Grille-pain', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(54, 'Friteuse', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(55, 'Verres à vin', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(56, 'Couverts', 6, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(57, 'Maison T4 (3 chambres salon)', 7, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(58, 'Maison T3 (2 chambres salon)', 7, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(59, 'Maison T2 (chambre salon)', 7, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(60, 'T1 ou Studio', 7, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(61, 'Appartement T4 (3 chambres)', 7, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(62, 'Appartement T3 (2 chambres)', 7, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(63, 'Appartement T2 (Chambre salon)', 7, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(64, 'Utilitaire ( Camion )', 8, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(65, 'Citadine', 8, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(66, 'Berline', 8, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(67, 'Familiale ( 7 places ) ', 8, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(68, '4x4', 8, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(69, 'SUV', 8, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(70, 'Minibus', 8, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(71, 'Climatisation', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(72, 'Sièges réglables ', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(73, 'Accoudoir central', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(74, 'Pare-soleil avec miroir éclairé', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(75, 'Airbags (frontaux, latéraux, rideaux)', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(76, 'Caméra de recul', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(77, 'Freinage automatique d’urgence', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(78, 'Navigation GPS', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(79, 'Commandes vocales', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(80, 'Connectivité Bluetooth et USB', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(81, 'Apple CarPlay / Android Auto', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(82, 'Pare-soleil intégrés', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(83, 'Régulateur de vitesse', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(84, 'Limiteur de vitesse', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(85, 'Caméra 360°', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(86, 'Toit ouvrant ou panoramique', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(87, 'Feux antibrouillard', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(88, 'Siège bébé ou fixation ISOFIX', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(89, 'Espace de rangement sous le coffre', 9, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(90, 'Minimum 3 jours', 12, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(91, '18 ans minimum', 12, '2024-11-22 07:59:53', '2024-12-02 14:39:02', NULL, 1, 1, NULL),
(92, 'Ancienneté de 2 ans de permis', 12, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(93, 'Ancienneté de plus de 3 ans de permis', 12, '2024-11-22 07:59:53', '2024-12-02 14:33:34', NULL, 1, 1, NULL),
(94, 'Ne doit pas traverser une frontière', 12, '2024-11-22 07:59:53', '2024-11-22 07:59:53', NULL, 1, 1, NULL),
(95, 'Spe1', 22, '2024-11-22 08:57:27', '2024-11-22 12:48:59', '2024-11-22 12:48:59', 1, 1, 1),
(96, 'cwe', 6, '2024-11-22 12:26:54', '2024-11-22 12:48:52', '2024-11-22 12:48:52', 1, 1, 1),
(97, 'fgbgbgb', 6, '2024-11-22 12:52:59', '2024-11-22 12:53:51', '2024-11-22 12:53:51', 1, 1, 1),
(98, 'frfefefre', 1, '2024-11-22 13:01:10', '2024-11-22 13:01:48', '2024-11-22 13:01:48', 1, 1, 1),
(99, 'cwecwcece', 6, '2024-11-22 13:05:02', '2024-11-22 13:21:01', '2024-11-22 13:21:01', 1, 1, 1),
(100, 'cwececwe', 20, '2024-11-22 13:05:09', '2024-11-22 13:20:53', '2024-11-22 13:20:53', 1, 1, 1),
(101, 'ecwece', 4, '2024-11-22 13:05:51', '2024-11-22 13:20:48', '2024-11-22 13:20:48', 1, 1, 1),
(102, 'dwdwedew', 6, '2024-11-22 13:06:03', '2024-11-22 13:20:45', '2024-11-22 13:20:45', 1, 1, 1),
(103, 'eerfrer', 1, '2024-11-22 13:11:51', '2024-11-22 13:20:41', '2024-11-22 13:20:41', 1, 1, 1),
(104, 'cdcdcfc', 6, '2024-11-22 13:12:27', '2024-11-22 13:20:37', '2024-11-22 13:20:37', 1, 1, 1),
(105, 'efrferfer', 12, '2024-11-22 13:12:48', '2024-11-22 13:20:31', '2024-11-22 13:20:31', 1, 1, 1),
(106, 'scdscsd', 1, '2024-11-22 13:18:08', '2024-11-22 13:19:04', '2024-11-22 13:19:04', 1, 1, 1),
(107, 'Commodité test 2', 1, '2024-11-23 18:39:48', '2024-11-23 18:40:08', '2024-11-23 18:40:08', 1, 1, 1),
(109, 'Ras', 6, '2024-11-23 20:05:59', '2024-12-02 12:51:28', '2024-12-02 12:51:28', 1, 1, 1),
(110, 'dwede', 1, '2024-11-23 20:08:28', '2024-12-02 12:51:23', '2024-12-02 12:51:23', 1, 1, 1),
(111, 'dwde', 1, '2024-11-23 20:09:50', '2024-12-02 12:51:19', '2024-12-02 12:51:19', 1, 1, 1),
(112, 'dewedw', 12, '2024-11-23 20:10:53', '2024-12-02 12:51:13', '2024-12-02 12:51:13', 1, 1, 1),
(125, 'Dîner sur place', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(126, 'à emporter', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(127, 'livraison', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(128, 'Wifi disponible', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(129, 'Réservation obligatoire', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(130, 'Parking', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(131, 'organisation d\'événement', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(132, 'payement par carte bancaire', 24, '2024-12-23 09:07:57', '2024-12-23 09:07:57', NULL, 1, 1, NULL),
(133, 'Test 123', 20, '2024-12-23 14:03:07', '2024-12-23 14:04:23', '2024-12-23 14:04:23', 1, 1, 1),
(134, 'Eq vie noc', 21, '2024-12-23 14:03:35', '2024-12-23 14:04:19', '2024-12-23 14:04:19', 1, 1, 1),
(135, 'test', 12, '2024-12-26 13:41:51', '2024-12-26 13:41:57', '2024-12-26 13:41:57', 1, 1, 1),
(136, 'Manuelle', 11, '2024-12-26 14:45:42', '2024-12-26 14:45:42', NULL, 1, 1, NULL),
(137, 'type1', 8, '2024-12-26 14:47:14', '2024-12-30 20:33:15', '2024-12-30 20:33:15', 1, 1, 1),
(138, 'type2', 8, '2024-12-26 14:47:49', '2024-12-30 20:33:11', '2024-12-30 20:33:11', 1, 1, 1),
(139, 'type3', 8, '2024-12-26 14:47:49', '2024-12-30 20:33:07', '2024-12-30 20:33:07', 1, 1, 1),
(140, 'moteur1', 18, '2024-12-26 14:55:28', '2024-12-30 20:33:03', '2024-12-30 20:33:03', 1, 1, 1),
(141, 'Coca-cola', 10, '2025-01-25 18:36:26', '2025-01-25 18:36:26', NULL, 21, 21, NULL),
(142, 'QWQ', 14, '2025-02-03 20:32:44', '2025-02-03 20:36:17', '2025-02-03 20:36:17', 1, 1, 1),
(143, 'ty1', 18, '2025-02-03 20:35:55', '2025-02-03 20:36:12', '2025-02-03 20:36:12', 1, 1, 1),
(144, 't2', 18, '2025-02-03 20:35:56', '2025-02-03 20:36:08', '2025-02-03 20:36:08', 1, 1, 1),
(145, 't1', 19, '2025-02-03 20:37:37', '2025-02-03 20:38:37', '2025-02-03 20:38:37', 1, 1, 1),
(146, 't2', 19, '2025-02-03 20:37:37', '2025-02-03 20:38:33', '2025-02-03 20:38:33', 1, 1, 1),
(147, 'e1', 21, '2025-02-03 20:37:55', '2025-02-03 20:38:29', '2025-02-03 20:38:29', 1, 1, 1),
(148, 'qw', 16, '2025-02-03 20:39:10', '2025-02-03 20:40:04', '2025-02-03 20:40:04', 1, 1, 1),
(149, 'qwqw', 14, '2025-02-03 20:39:28', '2025-02-03 20:40:02', '2025-02-03 20:40:02', 1, 1, 1),
(150, 'p1', 17, '2025-02-03 20:40:20', '2025-02-03 20:41:22', '2025-02-03 20:41:22', 1, 1, 1),
(151, 'e1', 14, '2025-02-03 20:40:33', '2025-02-03 20:41:19', '2025-02-03 20:41:19', 1, 1, 1),
(152, 'tr', 15, '2025-02-03 20:40:51', '2025-02-03 20:41:15', '2025-02-03 20:41:15', 1, 1, 1),
(153, 'qw', 21, '2025-02-03 20:41:45', '2025-02-03 20:42:17', '2025-02-03 20:42:17', 1, 1, 1),
(154, 'crer', 20, '2025-02-03 20:42:00', '2025-02-03 20:42:13', '2025-02-03 20:42:13', 1, 1, 1),
(155, 'Essence', 18, '2025-02-06 20:31:24', '2025-02-06 20:31:24', NULL, 1, 1, NULL),
(156, 'Gas-oil', 18, '2025-02-06 20:31:24', '2025-02-06 20:31:24', NULL, 1, 1, NULL),
(157, 'Hybride', 18, '2025-02-06 20:31:24', '2025-02-06 20:31:24', NULL, 1, 1, NULL),
(158, 'Équipements restauration 1', 14, '2025-02-16 07:37:04', '2025-02-16 07:37:04', NULL, 1, 1, NULL);

INSERT INTO `pays` (`id`, `nom`, `slug`, `code`, `indicatif`, `langue`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 'Togo', 'togo', 'TG', '+228', 'Français', '2023-12-12 13:12:42', '2025-02-08 14:08:45', NULL, 1, 1, NULL),
(2, 'France', 'france', 'FR', '+33', 'Français', '2025-02-03 20:30:03', '2025-02-03 20:31:23', '2025-02-03 20:31:23', 1, 1, 1),
(3, 'Bénin', 'benin', 'BJ', '+229', 'Français', '2025-02-08 14:03:37', '2025-02-08 14:03:37', NULL, 1, 1, NULL);

INSERT INTO `villes` VALUES (1,'Lomé','lome',1,'2023-12-12 13:12:42','2023-12-12 13:12:42',NULL,1,1,NULL),
(2,'Tsévié','tsevie',1,'2025-01-21 09:24:43','2025-01-21 09:24:43',NULL,21,21,NULL),
(3,'Sokodé','sokode',1,'2025-01-21 09:24:43','2025-01-21 09:24:43',NULL,21,21,NULL),
(4,'Aného','aneho',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(5,'Tabligbo','tabligbo',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(6,'Kévé','keve',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(7,'Vogan','vogan',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(8,'Baguida','baguida',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(9,'Aflao Gakli','aflao-gakli',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(10,'Agbodrafo','agbodrafo',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(11,'Avépozo','avepozo',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(12,'Togoville','togoville',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(13,'Zanguéra','zanguera',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(14,'Glidji','glidji',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(15,'Mission Tové','mission-tove',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(16,'Djagblé','djagble',1,'2025-01-21 09:31:37','2025-01-21 09:31:37',NULL,21,21,NULL),
(17,'Adétikopé','adetikope',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(18,'Atakpamé','atakpame',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(19,'Kpalimé','kpalime',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(20,'Badou','badou',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(21,'Notsé','notse',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(22,'Amlamé','amlame',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(23,'Agou','agou',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(24,'Danyi','danyi',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(25,'Amou Oblo','amou-oblo',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(26,'Kébo','kebo',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(27,'Akloa','akloa',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(28,'Kougnohou','kougnohou',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(29,'Gboto','gboto',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(30,'Glei','glei',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(31,'Hihéatro','hiheatro',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(32,'Tohoun','tohoun',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(33,'Tchamba','tchamba',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(34,'Blitta','blitta',1,'2025-01-21 09:31:50','2025-01-21 09:31:50',NULL,21,21,NULL),
(35,'Sotouboua','sotouboua',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(36,'Alédjo-Kadara','aledjo-kadara',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(37,'Kri-Kri','kri-kri',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(38,'Défalé','defale',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(39,'Adjengré','adjengre',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(40,'Langabou','langabou',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(41,'Tcharé','tchare',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(42,'Fazao','fazao',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(43,'Kara','kara',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(44,'Bafilo','bafilo',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(45,'Bassar','bassar',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(46,'Niamtougou','niamtougou',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(47,'Pagouda','pagouda',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(48,'Kandé','kande',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(49,'Kétao','ketao',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(50,'Koutammakou','koutammakou',1,'2025-01-21 09:32:14','2025-01-21 09:32:14',NULL,21,21,NULL),
(51,'Kabou','kabou',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(52,'Guérin Kouka','guerin-kouka',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(53,'Pya','pya',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(54,'Tchitchao','tchitchao',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(55,'Boufalé','boufale',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(56,'Dapaong','dapaong',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(57,'Cinkassé','cinkasse',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(58,'Mango','mango',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(59,'Tandjouaré','tandjouare',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(60,'Mandouri','mandouri',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(61,'Korbongou','korbongou',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(62,'Bogou','bogou',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(63,'Gando','gando',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(64,'Tami','tami',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(65,'Barkoissi','barkoissi',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(66,'Namoundjoga','namoundjoga',1,'2025-01-21 09:32:28','2025-01-21 09:32:28',NULL,21,21,NULL),
(67,'Paris','paris',2,'2025-02-03 20:30:19','2025-02-03 20:31:23','2025-02-03 20:31:23',1,1,1),
(69,'Abomey','abomey',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(70,'Abomey-Calavi','abomey-calavi',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(71,'Adja-Ouèrè','adja-ouere',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(72,'Adjarra','adjarra',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(73,'Adjohoun','adjohoun',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(74,'Aguégués','aguegues',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(75,'Akpro-Missérété','akpro-misserete',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(76,'Allada','allada',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(77,'Aplahoué','aplahoue',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(78,'Athiémé','athieme',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(79,'Avrankou','avrankou',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(80,'Banikoara','banikoara',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(81,'Bassila','bassila',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(82,'Bembéréké','bembereke',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(83,'Bohicon','bohicon',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(84,'Bopa','bopa',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(85,'Bonou','bonou',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(86,'Boukombé','boukombe',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(87,'Cobly','cobly',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(88,'Comé','come',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(89,'Copargo','copargo',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(90,'Cotonou','cotonou',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(91,'Covè','cove',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(92,'Dangbo','dangbo',3,'2025-02-08 14:24:36','2025-02-08 14:24:36',NULL,1,1,NULL),
(93,'Dassa-Zoumè','dassa-zoume',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(94,'Djakotomey','djakotomey',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(95,'Djidja','djidja',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(96,'Djougou','djougou',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(97,'Dogbo','dogbo',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(98,'Gogounou','gogounou',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(99,'Glazoué','glazoue',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(100,'Grand-Popo','grand-popo',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(101,'Houéyogbé','houeyogbe',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(102,'Ifangni','ifangni',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(103,'Kalalé','kalale',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(104,'Kandi','kandi',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(105,'Karimama','karimama',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(106,'Kérou','kerou',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(107,'Kétou','ketou',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(108,'Klouékanmè','klouekanme',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(109,'Kouandé','kouande',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(110,'Kpomassè','kpomasse',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(111,'Lalo','lalo',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(112,'Lokossa','lokossa',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(113,'Malanville','malanville',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(114,'Matéri','materi',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(115,'N\'Dali','ndali',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(116,'Natitingou','natitingou',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(117,'Nikki','nikki',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(118,'Ouinhi','ouinhi',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(119,'Ouaké','ouake',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(120,'Ouèssè','ouesse',3,'2025-02-08 14:24:57','2025-02-08 14:24:57',NULL,1,1,NULL),
(121,'Ouidah','ouidah',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(122,'Péhunco','pehunco',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(123,'Parakou','parakou',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(124,'Pèrèrè','perere',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(125,'Pobè','pobe',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(126,'Porto-Novo','porto-novo',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(127,'Sakété','sakete',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(128,'Savalou','savalou',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(129,'Savè','save',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(130,'Ségbana','segbana',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(131,'Sèmè-Kpodji','seme-kpodji',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(132,'Sinendé','sinende',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(133,'Sô-Ava','so-ava',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(134,'Tanguiéta','tanguieta',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(135,'Tchaourou','tchaourou',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(136,'Toffo','toffo',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(137,'Toribossito','toribossito',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(138,'Toucountouna','toucountouna',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(139,'Toviklin','toviklin',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(140,'Zè','ze',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(141,'Za-Kpota','za-kpota',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(142,'Zogbodomey','zogbodomey',3,'2025-02-08 14:25:13','2025-02-08 14:25:13',NULL,1,1,NULL),
(143,'Agbangnizoun','agbangnizoun',3,'2025-02-21 08:38:11','2025-02-21 08:38:11',NULL,1,1,NULL);

INSERT INTO `quartiers` (`id`, `nom`, `slug`, `ville_id`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 'Avedji', 'avedji', 1, '2023-12-12 13:12:42', '2023-12-12 13:12:42', NULL, 1, 1, NULL),
(2, 'Adidogomé', 'adidogome', 1, '2024-02-03 21:32:57', '2024-02-03 21:32:57', NULL, 2, 2, NULL),
(3, 'Soviépé', 'soviepe', 1, '2024-02-03 21:33:12', '2024-02-03 21:33:12', NULL, 2, 2, NULL),
(4, 'Soviépé, Totsi', 'soviepe-totsi', 1, '2024-12-09 16:31:19', '2024-12-09 16:31:19', NULL, 21, 21, NULL),
(7, 'Zanguéra', 'zanguera', 1, '2025-02-16 07:40:17', '2025-02-16 07:40:17', NULL, 1, 1, NULL);
