<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('annonces', function (Blueprint $table) {
            $table->foreignId('image')->nullable()->constrained('fichiers')->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('annonces', function (Blueprint $table) {
            try {
                $table->dropForeign(['image']);
                $table->dropColumn('image');
            } catch (\Throwable $th) {
                // throw $th;
            }
        });
    }
};
