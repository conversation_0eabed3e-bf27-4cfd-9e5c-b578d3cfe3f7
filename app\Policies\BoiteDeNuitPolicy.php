<?php

namespace App\Policies;

use App\Models\BoiteDeNuit;
use App\Models\User;

class BoiteDeNuitPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, BoiteDeNuit $boiteDeNuit): bool
    {
        //
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, BoiteDeNuit $boiteDeNuit): bool
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, BoiteDeNuit $boiteDeNuit): bool
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, BoiteDeNuit $boiteDeNuit): bool
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, BoiteDeNuit $boiteDeNuit): bool
    {
        //
    }
}
