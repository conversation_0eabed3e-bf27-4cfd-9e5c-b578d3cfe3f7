{{-- 
    Basic map scripts component for simple admin pages
    Usage: <x-admin.basic-map-scripts />
    
    Optional props:
    - mapId: ID of the map element (default: 'map')
    - center: Map center coordinates (default: Togo coordinates)
    - zoom: Initial zoom level (default: 6)
    - clickable: Whether map is clickable (default: true)
--}}

@props([
    'mapId' => 'map',
    'center' => ['lat' => 8.6195, 'lng' => 0.8248],
    'zoom' => 6,
    'clickable' => true
])

@push('scripts')
<script>
    var mymap;
    var marker;
    var mapHelper;

    // Initialize Google Maps using global system
    mapHelper = initGoogleMap('{{ $mapId }}', {
        center: { lat: {{ $center['lat'] }}, lng: {{ $center['lng'] }} },
        zoom: {{ $zoom }},
        clickable: {{ $clickable ? 'true' : 'false' }}
    }, function(helper, map) {
        mymap = map;
        marker = helper.marker;
        console.log('Basic admin map initialized');
    });
</script>
@endpush
