<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;
use Wildside\Userstamps\Userstamps;

class Ville extends Model
{
    use HasFactory, softDeletes, Userstamps;

    protected $fillable = [
        'nom',
        'slug',
        'pays_id',
    ];

    // mount
    public static function boot()
    {
        parent::boot();

        static::creating(function ($ville) {
            $ville->slug = Str::slug($ville->nom);
        });

        static::updating(function ($ville) {
            $ville->slug = Str::slug($ville->nom);
        });
    }

    protected $casts = [
        'nom' => PurifyHtmlOnGet::class,
    ];

    public function pays()
    {
        return $this->belongsTo(Pays::class);
    }

    public function quartiers()
    {
        return $this->hasMany(Quartier::class);
    }

    // nombre d'annonce liees a la ville
    public function getNombreAnnonce()
    {
        return $this->annonces->count();
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function annonces()
    {
        return $this->hasMany(Annonce::class);
    }
}
