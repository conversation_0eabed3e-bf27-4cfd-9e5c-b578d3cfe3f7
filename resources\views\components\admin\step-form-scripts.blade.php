{{-- 
    Step form validation scripts component
    Handles HTML5 validation issues with multi-step forms
    Usage: <x-admin.step-form-scripts />
--}}

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Step form validation scripts loaded');

    // Function to disable HTML5 validation for multi-step forms
    function disableHTML5ValidationForSteps() {
        console.log('Disabling HTML5 validation for step forms');

        // Find the main form
        const form = document.querySelector('form[wire\\:submit\\.prevent]');
        if (form) {
            // Disable HTML5 validation entirely for step forms
            form.setAttribute('novalidate', 'novalidate');
            console.log('HTML5 validation disabled for step form');
        }

        // Remove required attributes from hidden step fields
        document.querySelectorAll('.step-content.d-none [required]').forEach(function(field) {
            field.removeAttribute('required');
            field.setAttribute('data-step-required', 'true');
            console.log('Removed required from hidden field:', field.name || field.getAttribute('data-nom'));
        });
    }

    // Function to handle Livewire step changes
    function handleLivewireStepChanges() {
        // Listen for Livewire updates (step changes)
        document.addEventListener('livewire:updated', function() {
            console.log('Livewire updated - checking for step changes');

            setTimeout(function() {
                disableHTML5ValidationForSteps();
            }, 100);
        });
    }

    // Initialize
    function initialize() {
        console.log('Initializing step form validation fix');

        // Initial setup
        disableHTML5ValidationForSteps();
        handleLivewireStepChanges();
    }

    // Run initialization
    initialize();

    // Also run after Livewire is ready
    if (typeof Livewire !== 'undefined') {
        Livewire.hook('message.processed', function() {
            setTimeout(disableHTML5ValidationForSteps, 100);
        });
    }
});
</script>
@endpush
