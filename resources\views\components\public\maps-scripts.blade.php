@props([
    'mapId' => 'map',
    'latitude' => null,
    'longitude' => null,
    'editable' => true,  {{-- true = allow "Me localiser" + click on map --}}
    'zoom' => 13,
])

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    let map, marker;

    function initMap() {
        // Default center if no coordinates
        let center = {
            lat: {{ $latitude ?? 8.6195 }},
            lng: {{ $longitude ?? 0.8248 }}
        };

        // Initialize Google Map
        map = new google.maps.Map(document.getElementById('{{ $mapId }}'), {
            zoom: {{ $zoom }},
            center: center
        });

        // If we have coordinates, place marker
        if ({{ $latitude ? 'true' : 'false' }} && {{ $longitude ? 'true' : 'false' }}) {
            marker = new google.maps.Marker({
                position: center,
                map: map
            });
        }

        @if($editable)
        // Allow map click to set marker
        map.addListener("click", function(e) {
            placeMarker(e.latLng);
        });

        // "Me localiser" button
        // "Me localiser" button
document.querySelector('#locateBtn')?.addEventListener('click', function () {
    const spinner = document.getElementById('locateSpinner');
    const button = document.getElementById('locateBtn');

    if (navigator.geolocation) {
        // Disable button + show spinner
        button.disabled = true;
        spinner.style.display = 'inline-block';

        navigator.geolocation.getCurrentPosition(
            function (position) {
                let pos = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                map.setCenter(pos);
                placeMarker(pos);

                // Hide spinner + enable button again
                spinner.style.display = 'none';
                button.disabled = false;
            },
            function (error) {
                console.error("Error getting location:", error.message);

                // Hide spinner + enable button again
                spinner.style.display = 'none';
                button.disabled = false;

                alert("Impossible de récupérer votre position.");
            }
        );
    }
});


        @endif
    }

    // Place or move marker + update Livewire model
    function placeMarker(location) {
        if (marker) {
            marker.setPosition(location);
        } else {
            marker = new google.maps.Marker({
                position: location,
                map: map
            });
        }

        // Get coordinates with proper precision
        // Handle both LatLng objects and plain objects
        const lat = typeof location.lat === 'function'
            ? parseFloat(location.lat()).toFixed(8)
            : parseFloat(location.lat).toFixed(8);
        const lng = typeof location.lng === 'function'
            ? parseFloat(location.lng()).toFixed(8)
            : parseFloat(location.lng).toFixed(8);

        @if($editable)
            // Update Livewire fields directly as strings (matching validation rules)
            @this.set('latitude', lat.toString());
            @this.set('longitude', lng.toString());

            console.log('Coordinates updated:', { latitude: lat, longitude: lng });
        @else
            console.log('Map is read-only, coordinates not updated:', { latitude: lat, longitude: lng });
        @endif
    }

    // Initialize when Google script loads
    if (typeof google !== 'undefined') {
        initMap();
    } else {
        console.error("Google Maps API not loaded");
    }
});
</script>
@endpush
