/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #007fff !important;
}
.heading h2 span {
    color:#007fff;
}
.theme-cl {
    color:#007fff;
}
.theme-bg {
    background:#007fff;
}
span.like-listing i {
    background:#007fff;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#007fff;
}
.feature-box span {
    background:#007fff;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#007fff;
    border-color:#007fff;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#007fff;
    border-color:#007fff;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#007fff;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#007fff;
	border-color:#007fff;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#007fff;
}
span.category-tag {
    color:#007fff;
    border: 1px solid #007fff;
}
/*---prices---*/
.active .package-header {
    background:#007fff;
}
button.btn.btn-package {
    background:#007fff;
}
/*----button colors---*/
.theme-btn {
    background:#007fff;
    border: 1px solid #007fff;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#007fff;
    border: 1px solid #007fff;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #007fff;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#007fff;
	border-color:#007fff;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(0, 127, 255,0.1);
    color:#007fff;
    border-radius: 50px;
    border: 1px solid #007fff;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(0, 127, 255,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #007fff;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#007fff;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#007fff;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#007fff;
	border:1px solid #007fff;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#007fff;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#007fff;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#007fff;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#007fff;
}
a.btn.listing-btn {
    background:#007fff;
    border: 1px solid #007fff;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#007fff;
}
.title-content a{
	color:#007fff;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#007fff;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#007fff;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#007fff;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#007fff;
    border: 1px solid #007fff;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#007fff;
    background-color: rgba(0, 127, 255,0.12);
    border-color:#007fff;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#007fff;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#007fff;
}
.layout-option a:hover, .layout-option a:focus {
    color:#007fff;
}
.edit-info .btn {
    border: 1px solid #007fff;
    color:#007fff;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#007fff;
	background:#007fff;
}
ul.social-info.info-list li i {
    color:#007fff;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#007fff;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#007fff;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #007fff;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #007fff;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#007fff;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#007fff;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#007fff;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#007fff;
    border-bottom: 2px solid #007fff;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#007fff;
    border-bottom: 2px solid #007fff;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #007fff;
    background:#007fff;
}

.footer-copyright p a {
    color: #007fff;
}
.footer-social li a:hover i {
    background: #007fff;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #007fff;
    border: 1px solid #007fff;
}
.small-list-detail p a, p a {
    color: #007fff;
}
.quote-card::before {
    color:#007fff;
}
.quote-card cite {
    color:#007fff;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #007fff;
}
.service-box:before {
    border-left: 1px solid #007fff;
    border-right: 1px solid #007fff;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #007fff;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #007fff;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #007fff;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#007fff;
}
.service-box:after {
    border-bottom: 1px solid #007fff;
    border-top: 1px solid #007fff;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#007fff;
}
.range-slider .slider-selection {
    background:#007fff;
}
.range-slider .slider-handle.round {
    border: 2px solid #007fff;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #007fff;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#007fff !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#007fff !important;
}
}