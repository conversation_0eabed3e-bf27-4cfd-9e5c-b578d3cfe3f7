@extends('layout.public.app')

@section('title', 'Ajouter une Location Meublée')

@section('content')

    @php
        $breadcrumbs = [
            ['route' => 'accueil', 'label' => 'Accueil'],
            ['route' => 'public.annonces.create', 'label' => 'Déposer une annonce'],
            ['label' => 'Location meublée'],
        ];
    @endphp

    <x-breadcumb backgroundImage="{{ asset('assets_client/img/banner/image-2.jpg') }}" :showTitle="true"
        title="Ajouter une location meublée" :breadcrumbs="$breadcrumbs" />

    <div class="page-name location-meublee row">
        <div class="container text-left p-0">
            @livewire('admin.location-meublee.create')
        </div>
    </div>
@endsection

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.full.min.js"></script>
    <script>
        var mymap;
        var marker;
        var mapHelper;

        // Initialize Google Maps using global system
        mapHelper = initGoogleMap('map', {
            center: { lat: 8.6195, lng: 0.8248 },
            zoom: 6,
            clickable: true
        }, function(helper, map) {
            mymap = map;
            marker = helper.marker;
        });
    </script>

    <script>
        $('.select2').each(function() {
            $(this).select2({
                theme: 'bootstrap-5',
                dropdownParent: $(this).parent(),
            });
        });
    </script>
@endsection
