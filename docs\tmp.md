make:cast          Create a new custom Eloquent cast class
make:channel       Create a new channel class
make:command       Create a new Artisan command
make:component     Create a new view component class
make:controller    Create a new controller class
make:event         Create a new event class
make:exception     Create a new custom exception class
make:factory       Create a new model factory
make:job           Create a new job class
make:listener      Create a new event listener class
make:livewire      Create a new Livewire component
make:mail          Create a new email class
make:middleware    Create a new middleware class
make:migration     Create a new migration file
make:model         Create a new Eloquent model class
make:notification  Create a new notification class
make:observer      Create a new observer class
make:policy        Create a new policy class
make:provider      Create a new service provider class
make:request       Create a new form request class
make:resource      Create a new resource
make:rule          Create a new validation rule
make:scope         Create a new scope class
make:seeder        Create a new seeder class
make:test          Create a new test class
make:view          Create a new view