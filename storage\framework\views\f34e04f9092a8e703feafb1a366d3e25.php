<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['annonce']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['annonce']); ?>
<?php foreach (array_filter((['annonce']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="tab-content mt-3" id="myTabContent">
    <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
        <div class="side-list">
            <ul>
                <li>
                    <?php echo e($annonce->description ?? 'Aucune description disponible'); ?>

                </li>
            </ul>
        </div>
    </div>
    <div class="tab-pane fade" id="information" role="tabpanel" aria-labelledby="information-tab">
        <div class="side-list">
            <ul>
                <?php $__empty_1 = true; $__currentLoopData = $annonce->annonceable->caracteristiques; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <li>
                        <?php echo e($key); ?>

                        <span><?php echo e($value); ?></span>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <li>
                        Aucune information disponible
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
    <div class="tab-pane fade" id="equipement" role="tabpanel" aria-labelledby="equipement-tab">
        <?php $__empty_1 = true; $__currentLoopData = $annonce->referenceDisplay(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php if(count($value) > 0): ?>
                <div class="side-list">
                    <ul>
                        <li>
                            <?php echo e($key); ?>

                        </li>
                        <li class="detail-wrapper-body padd-bot-10">
                            <ul class="detail-check">
                                <?php $__empty_2 = true; $__currentLoopData = $value; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_2 = false; ?>
                                    <li><?php echo e($equipement); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_2): ?>
                                    <span class="text-center">
                                        Aucun équipement disponible
                                    </span>
                                <?php endif; ?>
                            </ul>
                        </li>
                    </ul>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-md-12">
                Aucun équipement disponible
            </div>
        <?php endif; ?>
    </div>

    <div class="tab-pane fade" id="menu" role="tabpanel" aria-labelledby="menu-tab">
        <div class="side-list">
            <ul>
                <?php $__empty_1 = true; $__currentLoopData = $annonce->annonceable->menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <li>
                        <div class="small-listing-box">
                            <div class="small-list-img">
                                <?php if($menu['image']): ?>
                                    <img class="img-responsive" src="<?php echo e(asset('storage/' . $menu['image'])); ?>" alt="">
                                <?php else: ?>
                                    <img class="img-responsive" src="<?php echo e(asset('assets/img/placeholder.svg')); ?>" alt="">
                                <?php endif; ?>
                            </div>
                            <div class="small-list-detail">
                                <h4><?php echo e($menu['nom']); ?></h4>
                                <p><?php echo e($menu['accompagnements']); ?></p>
                            </div>
                            <div class="small-list-action">
                                <span><?php echo e(number_format($menu['prix'], 0, ',', ' ')); ?> FCFA</span>
                            </div>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <li>
                        Aucune information disponible
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>

</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/components/public/show/fast-food-information-body.blade.php ENDPATH**/ ?>