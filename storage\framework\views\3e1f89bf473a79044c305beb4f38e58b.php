<?php $__env->startSection('title', 'Détails d\'une annonce'); ?>

<?php $__env->startSection('content'); ?>

    <?php
        $breadcrumbs = [['route' => 'accueil', 'label' => 'Accueil'], ['route' => 'search', 'label' => $annonce->type]];
    ?>

    <?php
        $typeList = $typeAnnonce ?? [];
    ?>
    <?php if (isset($component)) { $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcumb','data' => ['backgroundImage' => ''.e(asset('storage/' . $annonce->imagePrincipale->chemin)).'','showSearchButton' => true,'showTitle' => false,'breadcrumbs' => $breadcrumbs,'typeList' => $typeList]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['backgroundImage' => ''.e(asset('storage/' . $annonce->imagePrincipale->chemin)).'','showSearchButton' => true,'showTitle' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs),'typeList' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($typeList)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $attributes = $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $component = $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>

    <div class="page-name annonce-detail row">

        <!-- ================ Listing Detail Full Information ======================= -->
        <section class="list-detail p-0">
            <div class="container">
                <!-- Start: Pagination Wrapper -->
                <div class="row">
                    <div class="col-sm-12 nav-div nav-div-1">
                        <h5 style="text-align: left; border-bottom: 1px silver solid;" class="px-1 py-4">
                            <a href="<?php echo e(route('search')); ?>" title="Revenir à la recherche">
                                <i class="fa fa-fw fa-arrow-left" aria-hidden="true"></i>
                                Revenir à la recherche
                            </a>
                        </h5>
                    </div>
                    <div class="col-sm-12 nav-div" style="text-align: right">
                        <div class="d-flex px-4 py-1" style="justify-content: space-between; align-items: center;border-bottom: 1px silver solid;">
                            <a class="" href="<?php echo e($pagination->previous); ?>">
                                <i class="fa fa-fw fa-angle-left"></i>
                                Précédent
                            </a>
                            <span class="padd-l-10 padd-r-10 position-cl">
                                <?php echo e($pagination->position); ?>

                            </span>
                            <a class="" href="<?php echo e($pagination->next); ?>">
                                Suivant
                                <i class="fa fa-fw fa-angle-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End: Pagination Wrapper -->

                <div class="row">
                    <div class="col-md-8 col-sm-12">
                        <div class="widget-boxed padd-bot-10 pb-4">
                            <div class="widget-boxed-header">
                                <div class="listing-title-bar px-2">

                                    <h3> <?php echo e($annonce->titre); ?>


                                    </h3>
                                    <span class="mrg-l-5 category-tag"> <?php echo e($annonce->type); ?> </span>

                                </div>
                            </div>
                            <div class="widget-boxed-body padd-top-0 px-md-4">
                                <div class="annonces row gy-4">

                                    <div class="contact-item">
                                        <a href="<?php echo e(route('entreprise.show', $annonce->entreprise->slug)); ?>">
                                            <i class="fa fa-building fa-lg" style="color: #de6600"></i>
                                            <?php echo e($annonce->entreprise->nom); ?>

                                        </a>
                                    </div>

                                    <div class="contact-item">

                                        <?php if($annonce->entreprise->site_web): ?>
                                            <a href="<?php echo e($annonce->entreprise->site_web); ?>" target="_blank">
                                                <i class="ti-world" style="color: #de6600"></i> <?php echo e($annonce->entreprise->site_web); ?>

                                            </a>
                                        <?php endif; ?>

                                        <?php if($annonce->entreprise->email): ?>
                                            <a href="mailto:<?php echo e($annonce->entreprise->email); ?>">
                                                <i class="ti-email" style="color: #de6600"></i> <?php echo e($annonce->entreprise->email); ?>

                                            </a>
                                        <?php endif; ?>
                                    </div>
                                    <div class="counter-container" style="margin-right: 1rem;">
                                        <div class="counter-item-alt">
                                            <i class="fa fa-eye" aria-hidden="true"></i>
                                            <span style="white-space: nowrap;"><?php echo e($annonce->getViewCount()); ?> vue(s)</span>
                                        </div>
                                        <div class="counter-item-alt">
                                            <i class="fa fa-heart" aria-hidden="true"></i>
                                            <span style="white-space: nowrap;"><?php echo e($annonce->getFavoriteCount()); ?> favori(s)</span>
                                        </div>
                                        <div class="counter-item-alt">
                                            <i class="fa fa-comment" aria-hidden="true"></i>
                                            <span id="annonce-commentaire" style="white-space: nowrap;"><?php echo e($annonce->getCommentCount()); ?> commwentaire(s)</span>
                                        </div>
                                        <div class="counter-item-alt theme-btn border-0 text-white">
                                            <span id="annonce-note" style="white-space: nowrap;"><?php echo e($annonce->getNote()); ?>/5</span>
                                        </div>

                                    </div>
                                    <div class="social-links d-flex">

                                        <div class="d-flex justify-content-between" style="width: 100%;">
                                            <div class="d-flex">
                                                <?php if($annonce->entreprise->instagram): ?>
                                                    <a href="<?php echo e($annonce->entreprise->instagram); ?>" target="_blank" class="social-button instagram me-2">
                                                        <i class="fa-brands fa-instagram"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if($annonce->entreprise->facebook): ?>
                                                    <a href="<?php echo e($annonce->entreprise->facebook); ?>" target="_blank" class="social-button facebook me-2">
                                                        <i class="fa-brands fa-facebook"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if($annonce->entreprise->whatsapp): ?>
                                                    <a href="https://wa.me/<?php echo e($annonce->entreprise->quartier->ville->pays->indicatif ?? ''); ?><?php echo e(str_replace(' ', '', $annonce->entreprise->whatsapp)); ?>" target="_blank" class="social-button whatsapp me-2">
                                                        <i class="fa-brands fa-whatsapp"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                            <div class="side-list share-buttons">
                                                <div class="mrg-r-10">
                                                    <button class="buttons padd-10 btn-default share-button" data-toggle="modal" data-target="#share" onclick="shareAnnonce('<?php echo e(route('show', $annonce->slug)); ?>', '<?php echo e($annonce->titre); ?>', '<?php echo e(asset('storage/' . ($annonce->image ? $annonce->image : 'placeholder.jpg'))); ?>', '<?php echo e($annonce->type); ?>')">
                                                        <i class="fa fa-share-nodes"></i>
                                                        <!-- <span class="hidden-xs">Partager</span> -->
                                                    </button>
                                                </div>
                                                <div class="mrg-r-10">
                                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('public.favoris', [$annonce]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2684806185-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="widget-boxed padd-bot-10">
                            <div class="widget-boxed-header">
                                <div class="listing-title-bar">
                                    <h4 class="px-2 pb-3 pt-0 mt-0">
                                        Galérie
                                    </h4>
                                </div>
                            </div>
                            <div class="widget-boxed-body padd-top-0">
                                <div class="side-list no-border gallery-box">
                                    <div class="row mrg-l-5 mrg-r-10 mrg-bot-5">
                                        <div class="col-xs-12 col-md-12 p-0">
                                            <div id="carouselExampleIndicators" class="carousel slide" data-bs-ride="carousel">

                                                <div class="carousel-inner">
                                                    <?php $__currentLoopData = $annonce->galerieAvecImagePrincipale(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="carousel-item <?php echo e($key == 0 ? ' active' : ''); ?>">
                                                            <a href="<?php echo e(asset('storage/' . $image->chemin)); ?>" data-fancybox="gallery">
                                                                <img class="d-block w-100" style="object-fit: cover;" src="<?php echo e(asset('storage/' . $image->chemin)); ?>" alt="<?php echo e($annonce->titre); ?>" onerror="this.onerror=null; this.src='https://placehold.co/600';">
                                                            </a>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                                <div class="carousel-indicators">
                                                    <?php $__currentLoopData = $annonce->galerieAvecImagePrincipale(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <button class="active thumbnail" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="<?php echo e($key); ?>" type="button" aria-current="true" aria-label="Slide 1">
                                                            <a href="<?php echo e(asset('storage/' . $image->chemin)); ?>" data-fancybox="gallery-thumbs">
                                                                <img class="d-block w-100" style="object-fit: cover;" src="<?php echo e(asset('storage/' . $image->chemin)); ?>" alt="<?php echo e($annonce->titre); ?>" onerror="this.onerror=null; this.src='https://placehold.co/600';">
                                                            </a>
                                                        </button>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="test" class="widget-boxed padd-bot-10">
                            <div class="tab style-1 mrg-bot-40" role="tabpanel">
                                <?php echo e($annonce->annonceable->getShowInformationHeader()); ?>


                                <?php echo e($annonce->annonceable->getShowInformationBody()); ?>

                            </div>

                            <!-- Comments Section -->
                            <div class="widget-boxed padd-bot-10">
                                <div class="widget-boxed-header">
                                    <h4><i class="fa fa-comments padd-r-10"></i>Commentaires</h4>
                                </div>
                                <div class="widget-boxed-body">
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('public.comment', [$annonce]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2684806185-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-12">
                        <!-- Start: Localisation -->
                        <div class="widget-boxed padd-bot-10">

                            <div class="widget-boxed-header">
                                <h4 class="mt-2"><i class="ti-location-pin padd-r-10"></i>Localisation</h4>
                            </div>
                            <div class="widget-boxed-body padd-top-5">
                                <div class="side-list">
                                    <ul>
                                        <li>Pays : <b><?php echo e($annonce->getAdresseComplete()->pays ?? '-'); ?></b></li>
                                        <li>Ville : <b><?php echo e($annonce->getAdresseComplete()->ville ?? '-'); ?></b></li>
                                        <li>Quartier : <b><?php echo e($annonce->getAdresseComplete()->quartier ?? '-'); ?></b></li>

                                        <li>
                                            <div id="map" class="full-width" style="height:252px;"></div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <!-- End: Localisation -->

                        <!-- Start: Opening hour -->
                        <div class="widget-boxed padd-bot-10">
                            <div class="widget-boxed-header">
                                <h4 class="mt-2"><i class="ti-time padd-r-10"></i>Heures d'ouverture</h4>
                            </div>
                            <div class="widget-boxed-body">
                                <div class="side-list">
                                    <ul>
                                        <?php $__currentLoopData = $annonce->entreprise->heure_ouvertures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $ouverture): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($ouverture == 'Fermé'): ?>
                                                <li><?php echo e($key); ?> <span class="text-danger"><?php echo e($ouverture); ?></span></li>
                                            <?php else: ?>
                                                <li><?php echo e($key); ?> <span><?php echo e($ouverture); ?></span></li>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <!-- End: Opening hour -->

                    </div>
                    <div class="col-md-4 col-sm-12">

                    </div>

                </div>
            </div>

        </section>
        <!-- ================ Listing Detail Full Information ======================= -->

        <?php echo $__env->make('public.gallery', [
            'galerie' => $annonce->galerie,
            'couverture' => $annonce->imagePrincipale,
        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->make('components.public.share-modal', [
            'title' => 'Partager cette annonce',
            'annonce' => $annonce,
        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->stopSection(); ?>

    <!-- Map Scripts Component -->
    <?php if (isset($component)) { $__componentOriginal68ae95b500761c8e20cbd39dee90f904 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68ae95b500761c8e20cbd39dee90f904 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.public.maps-scripts','data' => ['latitude' => $annonce->latitude,'longitude' => $annonce->longitude,'editable' => false,'zoom' => 13,'mapId' => 'map']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('public.maps-scripts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['latitude' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($annonce->latitude),'longitude' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($annonce->longitude),'editable' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'zoom' => 13,'mapId' => 'map']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68ae95b500761c8e20cbd39dee90f904)): ?>
<?php $attributes = $__attributesOriginal68ae95b500761c8e20cbd39dee90f904; ?>
<?php unset($__attributesOriginal68ae95b500761c8e20cbd39dee90f904); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68ae95b500761c8e20cbd39dee90f904)): ?>
<?php $component = $__componentOriginal68ae95b500761c8e20cbd39dee90f904; ?>
<?php unset($__componentOriginal68ae95b500761c8e20cbd39dee90f904); ?>
<?php endif; ?>

    <?php $__env->startSection('js'); ?>
        <script>
            // Initialize Fancybox for gallery images
            $(document).ready(function() {
                $('[data-fancybox="gallery"]').fancybox({
                    buttons: [
                        "zoom",
                        "fullScreen",
                        "close"
                    ],
                    animationEffect: "fade",
                    transitionEffect: "fade",
                    loop: true,
                    protect: true,
                    modal: false,
                    idleTime: 3,
                    clickContent: function(current, event) {
                        return current.type === "image" ? "zoom" : false;
                    }
                });

                $('[data-fancybox="gallery-thumbs"]').fancybox({
                    buttons: [
                        "zoom",
                        "fullScreen",
                        "close"
                    ],
                    animationEffect: "fade",
                    transitionEffect: "fade",
                    loop: true,
                    protect: true
                });
            });

            function shareAnnonce(url, titre, image, type) {
                console.log("share function called with:", url, titre, image, type);
                var text = "Salut!%0AJette un œil à l'annonce que j'ai trouvé sur Vamiyi%0ATitre : " + titre + "%0ALien : " + url + " ";
                var subject = titre;

                // Set content
                $('#annonce-titre').text(subject);
                $('#annonce-image-url').attr('src', image);
                $('#annonce-type').text(type);

                // Set share links
                $('#annonce-email').attr('href', 'mailto:?subject=' + subject + '&body=' + text);
                $('#annonce-url').data('url', url);
                $('#annonce-facebook').attr('href', 'https://www.facebook.com/sharer/sharer.php?u=' + url);
                $('#annonce-whatsapp').attr('href', 'whatsapp://send?text=' + text);

                // Hide page zone and show modal
                $('#share-page-zone').hide();

                // Properly open Bootstrap modal
                $('#share').modal('show');
            }
        </script>

        <script>
            $(document).ready(function() {
                $('.image-preview').click(function() {
                    $('#share').hide();
                    var id = $(this).data('id');
                    $('#image-' + id).addClass('pulse');
                    setTimeout(() => {
                        $('#image-' + id).removeClass('pulse');
                    }, 2000);
                });
            });
        </script>
    <?php $__env->stopSection(); ?>

    <?php $__env->startSection('css'); ?>
        <style>
            /* Fancybox customization */
            .fancybox-bg {
                background: #000;
            }

            .fancybox-is-open .fancybox-bg {
                opacity: 0.9;
            }

            .fancybox-caption {
                font-size: 16px;
            }

            /* Make carousel images clickable */
            .carousel-item a {
                cursor: zoom-in;
                display: block;
            }

            /* Fix for thumbnail buttons */
            .carousel-indicators button a {
                display: block;
                width: 100%;
                height: 100%;
            }
        </style>
    <?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.public.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/public/show.blade.php ENDPATH**/ ?>