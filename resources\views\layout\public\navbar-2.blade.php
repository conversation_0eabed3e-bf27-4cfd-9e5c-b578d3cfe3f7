@props(['login' => true])
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">

<style>
    body.home-2 nav.navbar {
        background-color: #ffffff;
        border-bottom: none;
        -webkit-box-shadow: 0 2px 4px 0 rgba(188, 195, 208, 0.5);
        -moz-box-shadow: 0 2px 4px 0 rgba(188, 195, 208, 0.5);
        box-shadow: 0 2px 4px 0 rgba(188, 195, 208, 0.5);
        z-index: 999;
        padding: 1rem 3rem;
    }

    nav:not(.navbar-transparent) .logo-scrolled,
    nav.navbar-transparent .logo-display {
        display: block;
        width: 70px;
        height: 70px;
        max-height: 80px;
        margin-top: -10px;
    }

    /* Smartphones (portrait) */
    @media (max-width: 480px) {
        .header {
            display: none
        }

        #header-mobile {
            display: block !important
        }
    }

    /* Smartphones (landscape) */
    @media (min-width: 481px) and (max-width: 767px) {
        .header {
            display: none
        }

        #header-mobile {
            display: block !important
        }
    }

    /* Tablets (portrait and landscape) */
    @media (min-width: 768px) and (max-width: 1024px) {
        .header {
            display: none
        }

        #header-mobile {
            display: block !important
        }
    }


    .nav-tabs {
        border-bottom: 1px solid #dee2e6;
    }

    .nav-tabs .nav-link {
        color: #333;
        font-weight: bold;
        border: none;
        padding: 0.5rem 0;
        margin-right: 2rem;
        position: relative;
    }

    .nav-tabs .nav-link.active {
        color: #000;
        font-weight: bold;
        border: none;
        background: none;
    }

    .nav-tabs .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #000;
    }

    .search-container {

        border-radius: 25px;
    }

    body.home-2 a.btn i {
        margin: 0 !important
    }

    .search-input {
        background: transparent;
        border: none;
        outline: none;
        width: 100%;
        padding: 10px 15px;
    }

    .search-input:focus {
        outline: none;
    }

    .category-item {
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .jardin-bg {
        background-color: #d1f5f0;
    }

    .chambre-bg {
        background-color: #ffd6d1;
    }

    .category-white {
        background-color: #f5f5f7;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 10px;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        width: 80%;
        /* Not full width as per design */
        height: 100%;
        background: white;
        z-index: 1050;
        overflow-y: auto;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        display: none;
    }

    .overlay.show {
        display: block;
    }

    .search-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: white;
        z-index: 1050;
        overflow-y: auto;
        transform: translateY(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .search-overlay.show {
        transform: translateY(0);
    }

    .search-result {
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }

    .close-btn {
        background-color: #f0f0f0;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
    }

    .category-icon {
        width: 40px;
        height: 40px;
        object-fit: contain;
    }

    .tab-icon {
        font-size: 1.2rem;
        margin-right: 8px;
    }

    #header-mobile {
        display: none
    }

    .list-none {
        list-style: none;
    }
</style>

<div class="header">
    <nav class="navbar navbar-expand-lg bg-light fixed-top bootsnav navbar-transparent">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ route('accueil') }}">
                {{-- @if ($active == 'login')
                    <img class="logo logo-display d-inline-block align-text-top" src="{{ asset('assets/img/logo-vamiyi-vacances-togo.svg') }}" alt="">
                @else --}}
                <img class="logo logo-display d-inline-block align-text-top"
                    src="{{ asset('assets/img/logo-vamiyi-vacances-white.svg') }}" alt="">
                {{-- @endif --}}
                <img class="logo logo-scrolled d-inline-block align-text-top"
                    src="{{ asset('assets/img/logo-vamiyi-vacances-togo.svg') }}" alt="">
                <span>Vamiyi</span>
            </a>
            <button class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#navbar-list"
                type="button" aria-controls="navbar-list" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbar-list">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('search', ['se_loger' => 1]) }}">Se loger</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('search', ['se_restaurer' => 1]) }}">Se restaurer</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('search', ['sortir' => 1]) }}">Sortir</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('search', ['louer_voiture' => 1]) }}">Louer une voiture</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="user-account">
            <ul>
                <li class="">
                    @if (request()->is('login'))
                        <a class="btn theme-btn" href="{{ route('register') }}">
                            <i class="ti-user" aria-hidden="true"></i> <span>Inscription </span>
                        </a>
                    @else
                        <a class="btn theme-btn" href="{{ route('login') }}">
                            <i class="ti-user" aria-hidden="true"></i> <span>Connexion</span>
                        </a>
                    @endif
                </li>
            </ul>
        </div>
    </nav>
</div>


<!-- Mobile Header -->
<div id="header-mobile">
    <header class="container-fluid py-3">
        <div class="row align-items-center">
            <div class="col-2">
                <button class="btn p-0 border-0" id="menuToggle">
                    <i class="bi bi-list fs-4" style="font-size:32px !important"></i>
                </button>
            </div>
            <div class="col-8 text-center">
                <div class="logo">
                    <img onclick="window.location.href='/'" style="width: 70px;
    height: 70px;
    max-height: 80px;"
                        class="logo logo-scrolled d-inline-block align-text-top"
                        src="{{ asset('assets/img/logo-vamiyi-vacances-togo.svg') }}" alt="">

                </div>
            </div>
            <div class="col-2 d-flex justify-content-end gap-2">

                <a>
                    <i>
                        @if (request()->is('login'))
                            <a class="btn theme-btn" href="{{ route('register') }}">
                                <i class="ti-user" aria-hidden="true"></i> <span>Inscription </span>
                            </a>
                        @else
                            <a class="btn theme-btn" href="{{ route('login') }}">
                                <i class="ti-user" aria-hidden="true"></i> <span>Connexion</span>
                            </a>
                        @endif
                    </i>
                </a>
            </div>
        </div>


    </header>

    <!-- Background Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Sidebar Menu -->
    <div class="sidebar" id="sidebar">
        <div class="p-4">
            <div class="d-flex justify-content-between mb-4">
                <img style="width: 70px;
    height: 70px;
    max-height: 80px;" onclick="window.location.href='/'"
                    class="logo logo-scrolled d-inline-block align-text-top"
                    src="{{ asset('assets/img/logo-vamiyi-vacances-togo.svg') }}" alt="">

                <button class="close-btn" id="closeSidebar">
                    <i class="bi bi-x fs-4"></i>
                </button>
            </div>


            <div class="category-white d-flex justify-content-between align-items-center"
                onclick="window.location.href='{{ route('search', ['se_loger' => 1]) }}'">
                <div class="d-flex align-items-center">

                    <a class="nav-link">Se loger</a>
                </div>
                <i class="bi bi-chevron-right"></i>
            </div>

            <div class="category-white d-flex justify-content-between align-items-center"
                onclick="window.location.href='{{ route('search', ['se_restaurer' => 1]) }}'">
                <div class="d-flex align-items-center">
                    <a class="nav-link">Se restaurer</a>
                </div>
                <i class="bi bi-chevron-right"></i>
            </div>

            <div class="category-white d-flex justify-content-between align-items-center"
                onclick="window.location.href='{{ route('search', ['sortir' => 1]) }}'">
                <div class="d-flex align-items-center">
                    <a class="nav-link">Sortir</a>
                </div>
                <i class="bi bi-chevron-right"></i>
            </div>

            <div class="category-white d-flex justify-content-between align-items-center"
                onclick="window.location.href='{{ route('search', ['louer_voiture' => 1]) }}'">
                <div class="d-flex align-items-center">
                    <a class="nav-link">Louer une voiture</a>

                </div>
                <i class="bi bi-chevron-right"></i>
            </div>
            <form class="mt-4">
                @if (auth()->check() && (auth()->user()->hasRole('Professionnel') || auth()->user()->hasRole('Administrateur')))
                    <a class="btn add-annonce" id="btn-deposer-annonce"
                        href="{{ route('public.annonces.create') }}">
                        <i class="fa-solid fa-plus"></i>Déposer une annonce
                    </a>
                @elseif (auth()->check() && auth()->user()->hasRole('Usager'))
                    <a class="btn add-annonce" id="btn-deposer-annonce" href="{{ route('pricing') }}">
                        <i class="fa-solid fa-plus"></i>Déposer une annonce
                    </a>
                @else
                    <a class="btn add-annonce" id="btn-deposer-annonce" data-bs-toggle="modal"
                        data-bs-target="#signin" href="javascript:void(0)" onclick="$('#share').hide()">
                        <i class="fa-solid fa-plus"></i>Déposer une annonce
                    </a>
                @endif
            </form>
        </div>
    </div>


    <script>
        // Toggle sidebar
        document.getElementById('menuToggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.add('show');
            document.getElementById('overlay').classList.add('show');
        });

        document.getElementById('closeSidebar').addEventListener('click', function() {
            document.getElementById('sidebar').classList.remove('show');
            document.getElementById('overlay').classList.remove('show');
        });

        document.getElementById('overlay').addEventListener('click', function() {
            document.getElementById('sidebar').classList.remove('show');
            document.getElementById('overlay').classList.remove('show');
        });
    </script>
</div>
