<div class="fast-food-template">
    <div>
        <form wire:submit.prevent="store" enctype="multipart/form-data">
            @csrf
            <div class="card-header bg-vamiyi-orange text-white text-center py-4">
                <h2 class="card-title font-weight-bold">Remplissez les informations </h2>
            </div>
            <div class="card-body pt-4">
                    @php
                        $steps = ['Entreprise', 'Location', 'Informations', 'Menus', 'Images'];
                        $icons = ['fa-building', 'fa-map-marker-alt', 'fa-info-circle', 'fa-utensils', 'fa-images'];
                    @endphp
                    
                    <x-admin.form-stepper :steps="$steps" :currentStep="$currentStep" :icons="$icons" />

                <!-- Step 1: Entreprise -->
                <div class="step-content {{ $currentStep == 0 ? '' : 'd-none' }}">
                    <div class="row align-items-start">
                            @include('admin.annonce.entreprise-template', [
                                'entreprises' => $entreprises,
                            ])
                    </div>
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />

                </div>


                <!-- Step 2: Location -->
                <div class="step-content {{ $currentStep == 1 ? '' : 'd-none' }}">
                        <div class="row align-items-start">
                            @include('admin.annonce.location-template', [
                                'pays' => $pays,
                                'villes' => $villes,
                                'quartiers' => $quartiers,
                            ])
                        </div>
                    
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />
                </div>


                <!-- Step 2: Informations -->
                <div class="step-content {{ $currentStep == 2 ? '' : 'd-none' }}">
                    <div class="row align-items-start">
                        @include('admin.annonce.description-component')
                    </div>
                    <div class="row align-items-start">
                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Équipements',
                            'name' => 'equipements_restauration',
                            'options' => $list_equipements_restauration,
                        ])

                        @include('admin.annonce.reference-select-component', [
                            'title' => 'Services proposés',
                            'name' => 'services',
                            'options' => $list_services,
                        ])
                    </div>
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="3" />
                </div>

                <!-- Step 3: Menus -->
                <div class="step-content {{ $currentStep == 3 ? '' : 'd-none' }}">
                    <div class="row align-items-start">
                        <div class="col-md-12 col-sm-12 p-0">
                            <div class="col produits">
                                <h3>Menus ({{ count($produits) }})
                                    <b style="color: red; font-size: 100%;">*</b>
                                </h3>
                                <h4>Carte de menus</h4>
                                <div id="produits-container">
                                    <!-- Produit 1 par défaut -->
                                    @foreach ($produits as $index => $menu)
                                        <div id="produit-item-{{ $index + 1 }}" class="form-group produit-item">
                                            <div>
                                                <button class="btn btn-form" data-bs-toggle="offcanvas" data-bs-target="#produit-{{ $index + 1 }}" type="button" aria-controls="produit-{{ $index + 1 }}">
                                                    Menu {{ $index + 1 }} : {{ $menu['nom'] }} <i class="fa fa-pencil"></i>
                                                </button>
                                            </div>
                                            <div id="produit-{{ $index + 1 }}" class="offcanvas offcanvas-end" data-bs-scroll="true" aria-labelledby="produit-{{ $index + 1 }}" tabindex="-1">
                                                <div class="offcanvas-header">
                                                    <h5 class="offcanvas-title">Menu {{ $index + 1 }}</h5>
                                                    <button id="produits-close-{{ $index + 1 }}" class="btn-close text-reset" data-bs-dismiss="offcanvas" type="button" aria-label="Close"></button>
                                                </div>
                                                <div class="offcanvas-body">
                                                    <div class="form-group">
                                                        <label for="produit-name-{{ $index + 1 }}">Nom<b style="color: red; font-size: 100%;">*</b></label>
                                                        <input id="produit-name-{{ $index + 1 }}" class="form-control required-field" type="text" wire:model="produits.{{ $index }}.nom">
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="produit-description-{{ $index + 1 }}">Accompagnements<b style="color: red; font-size: 100%;">*</b></label>
                                                        <textarea id="produit-description-{{ $index + 1 }}" class="form-control required-field" wire:model="produits.{{ $index }}.accompagnements" rows="3"></textarea>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="produit-price-{{ $index + 1 }}">Prix<b style="color: red; font-size: 100%;">*</b></label>
                                                        <input id="produit-price-{{ $index + 1 }}" class="form-control required-field" type="number" wire:model="produits.{{ $index }}.prix">
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="form-img-produit-{{ $index + 1 }}">Image à la Une <b style="color: red; font-size: 100%;">*</b></label>
                                                        <input id="form-img-produit-{{ $index + 1 }}" class="form-control form-control-file" data-id="{{ $index + 1 }}" type="file" wire:model="produits.{{ $index }}.image" accept="image/*">

                                                        @if (!empty($produits[$index]['image']))
                                                            <img class="listing-shot-img img-responsive" src="{{ $produits[$index]['image']->temporaryUrl() }}" alt="" style="width: 100%; height: 100px; object-fit: cover;">
                                                        @endif

                                                    </div>
                                                    <button class="btn btn-danger delete-produit-btn mb-2" data-produit-id="{{ $index + 1 }}" type="button" wire:click="removeProduit({{ $index }})">Supprimer</button>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach

                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span id="produit-error-message" class="text-danger"><br></span>
                                    </div>
                                    @if ($produits_error)
                                        <div class="col-md-12 col-sm-12 text-center">
                                            <span class="text-danger">{{ $produits_error }}</span>
                                        </div>
                                    @endif
                                    @error('produits.*.nom')
                                        <div class="col-md-12 col-sm-12 text-center">
                                            <span class="text-danger">{{ $message }}</span>
                                        </div>
                                    @enderror
                                    @error('produits.*.accompagnements')
                                        <div class="col-md-12 col-sm-12 text-center">
                                            <span class="text-danger">{{ $message }}</span>
                                        </div>
                                    @enderror
                                    @error('produits.*.prix')
                                        <div class="col-md-12 col-sm-12 text-center">
                                            <span class="text-danger">{{ $message }}</span>
                                        </div>
                                    @enderror
                                    @error('produits.*.image')
                                        <div class="col-md-12 col-sm-12 text-center">
                                            <span class="text-danger">{{ $message }}</span>
                                        </div>
                                    @enderror
                                    <button id="add-produit-btn" class="btn btn-success btn-square" type="button"><i class="fa fa-plus"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />
                </div>


                

                <!-- Step 4: Images -->
                <div class="step-content {{ $currentStep == 4 ? '' : 'd-none' }}">
                    <div class="row align-items-start">
                        @include('admin.annonce.create-galery-component', [
                            'galery' => $galerie,
                        ])
                    </div>

                   <div class="d-flex justify-content-between my-4 {{ $currentStep == 4 ? '' : 'd-none' }}">
                        <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                            Retour
                        </button>
                        <button id="restaurant-form-submit" class="btn theme-btn" type="submit" style="margin-right: 30px;" wire:loading.attr="disabled">
                                    <i class="fa fa-save fa-lg" style="margin-right: 10px;"></i>
                                    Enregistrer
                                </button>
                    </div>

                </div>
            </div>
        </form>
    </div>
</div>



<script>
    window.addEventListener('console-log', event => {
        console.log(event.detail.message);
    });
</script>