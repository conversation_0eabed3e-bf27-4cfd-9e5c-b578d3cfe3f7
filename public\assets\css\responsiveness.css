/*-----------------------------------------
* Theme Name: Listing Hub
* Author: Themez <PERSON>b
* Version: 1.0
* Last Change: Dec 27 2017
  Author URI    : http://www.themezhub.com/
 --------------------------------*/
@media only screen  and (min-width : 768px) {
	.form-control.right-br{
		border-right:1px solid #e4e4e4;
	}
	
	body.home-2 .banner p{
		max-width:85%;
	}
	
	.banner-caption .form-control.left-radius{
	border-radius:50px 0px 0px 50px ;
	}
	.banner .btn.btn-default{
	border-radius:0px 50px 50px 0px;
	}
	.banner p {
		margin-bottom: 30px;
	}
}
	
@media only screen  and (min-width : 993px) {
	ul.nav.navbar-nav.navbar-right>li {
		position: fixed;
		top: 0;
		margin: 0;
		right: 0;
		padding: 0 !important;
	}
	nav.navbar.bootsnav ul.nav > li {
		padding: 4px 15px;
	}
	nav.navbar.bootsnav ul.nav > li > a {
		padding: 15px 9px 18px 9px;
	}
	body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
		background:#de6600 !important;
	}
	body.home-2 nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
		background:transparent !important;
	}
	body nav.navbar.bootsnav ul.nav > li > a.addlist {
		background: #de6600 !important;
		color: #ffffff !important;
		border-radius: 0;
		padding: 20.5px 35px !important;
		margin-top: 0 !important;
		text-align: center;
	}
	.navbar>.container .navbar-brand, .navbar>.container-fluid .navbar-brand {
		margin-left:0px;
	}
	
}
@media only screen  and (min-width : 1024px) {
	h1 {
	  font-size:46px;
	}
	h2 {
	  font-size:36px;
	}
	h3 {
	  font-size:27px;
	}
	h4 {
	  font-size:20px;
	} 
	
	h5 {
	  font-size:17;
	}
	
	h6 {
	  font-size:12;
	}
	nav.navbar.bootsnav li.dropdown ul.dropdown-menu {
		-webkit-box-shadow:0px 0px 15px 1px rgba(113, 106, 202, 0.2);
		box-shadow:0px 0px 15px 1px rgba(113, 106, 202, 0.2);
		border-radius:4px;
		padding: 0;
		width: 200px;
		background: #fff;
		border:none;
		border-top:none;
	}
	nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a {
		display: block;
		padding: 14px 12px 14px 12px;
		clear: both;
		line-height: 1.42857143;
		color: #67757c;
		border-bottom: 1px solid #f1f6f9;
	}
	nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus{
		color:#de6600;
	}
	nav.navbar.bootsnav ul.nav > li > a {
    padding: 16.5px 10px 16.5px 10px;
	}
	nav.navbar.bootsnav ul.navbar-right li.dropdown ul.dropdown-menu.navbar-left li a {
		text-align: left;
	}

	/*-----------------banner style-------------*/
	.banner{
	padding-top:13%;
	height:100vh;
	min-height:600px;
	}

	.banner-caption .form-control{
	height:70px;
	color:#90969e;
	font-size:15px;
	}
	.banner .btn.btn-default{
	height:70px;
	}
	.banner .form-control .btn.dropdown-toggle.btn-default, .banner .form-control .btn.dropdown-toggle.btn-default:hover, .banner .form-control .btn.dropdown-toggle.btn-default:focus {
    height:70px;
	}
	
	i.banner-icon {
    top: 24px;
    font-size: 20px;
	}
	body a.btn {
		padding: 15px 40px;
	}
	.pulse-tag {
		margin: 5em auto 0 auto;
	}
	
	/*------------ Custom Font Style --------------*/
	.font-50{
	font-size:50px;
	}

	.font-60{
		font-size:60px;
	}

	.font-80{
		font-size:80px;
	}

	.font-100{
		font-size:100px;
	}

	.font-150{
		font-size:150px;
	}

	.font-200{
		font-size:200px;
	}

	.font-250{
		font-size:250px;
	}

	.font-300{
		font-size:300px;
	}

	.font-400{
		font-size:400px;
	}

	.font-450{
		font-size:450px;
	}

	.font-500{
		font-size:500px;
	}

	.font-bold{
		font-weight:bold;
	}

}

@media only screen  and (min-width : 1200px) {
	nav.navbar.bootsnav ul.nav > li > a {
		padding: 15px 9px 18px 9px;
	}

}

@media only screen and (max-width: 1023px) and (min-width: 993px){

}

@media screen and (max-width: 1199px) {
	.footer-social li a i {
		width: 45px;
		height: 45px;
		font-size: 16px;
		line-height: 45px;
	}
	.footer-social li {
		padding: 5px 4px 5px 5px;
	}
}

@media only screen and (max-width: 992px) and (min-width: 768px){

}
@media screen and (max-width: 992px) {
	body.home-2 nav.navbar.bootsnav.navbar-transparent {
		background-color: #ffffff;
		border-bottom: 1px solid rgba(255,255,255,0.10);
	}
	nav.navbar.bootsnav .navbar-nav > li > a {
		display: block;
		width: 100%;
		border-bottom: solid 1px #dde6ef;
		padding: 18px 0;
		margin-bottom: -1px;
		color: #445461 !important;
	}
	nav.navbar.bootsnav .navbar-toggle {
		font-size: 20px;
		top: 8px;
	}
	nav.navbar.bootsnav.navbar-fixed .logo-display, nav.navbar.bootsnav.navbar-fixed .logo-scrolled {
		max-height: 40px;
	}
}

@media screen and (max-width: 767px) {
	/*-- General Style--*/
	html body .mob-padd-0{
		padding:0;
	}
	html body .mob-mrg-0{
		margin:0;
	}
	html body .mob-extra-mrg{
		margin-left:-15px;
		margin-right:-15px;
	}
	.heading h2 {
		font-size: 28px;
	}
	/*----- Mobile Padding Settings ------*/
	.mob-padd-0{
		padding-left:0;
		padding-right:0;
	}
	.mob-mrg-0{
		margin-left:-15px;
		margin-right:-15px;
	}
	.mob-extra-mrg{
		margin-left:-15px;
		margin-right:-15px;
	}
	/*----Banner Style--*/
	.banner {
		padding: 120px 0 80px 0;
	}
	.banner-text {
    padding: 0;
	}
	body.home-2 .banner h1 {
    font-size:26px;
	}
	body.home-2 .banner p {
    max-width: 100%;
    font-size: 14px;
	}
	.banner p {
		font-size: 15px;
		line-height: 1.6;
	}
	.heading {
		padding: 0px 0 20px 0;
		margin-bottom: 0;
	}
	.heading p {
		line-height: 1.6;
		font-size: 15px;
	}
	.feature-box p {
		font-size: 14px;
	}
	.feature-box span {
		width: 65px;
		height: 65px;
		line-height: 65px;
		font-size: 23px;
	}
	.tag-content p {
		font-size: 14px;
		line-height: 1.7;
	}
	
	.verticleilist.listing-shot {
    display: block;
	}
	.verticleilist.listing-shot .verticle-listing-caption {
		flex: auto;
		padding: 35px 0 0 0;
	}
	.verticleilist.listing-shot .listing-item {
		flex: auto;
	}
	.service-box .read a{
		opacity:1;
	}
	/*-----Listing Detail----*/
	.detail-wrapper-body {
		padding: 1.5em 1em 1.5em 1em;
	}
	.detail-wrapper-body ul.detail-check li {
		width: 100%;
	}
	.detail-wrapper-body .review-list li {
		padding: 2em 0em;
	}
	.review-avatar {
		width: 60px;
		height: 60px;
	}
	.review-body {
		padding-left: 70px;
	}
	.review-content p {
		font-size: 14px;
		line-height: 1.7;
	}
	ul.side-list-inline.social-side li {
		padding: 10px 0px 4px 0px;
	}
	.listing-shot-img {
		overflow: hidden;
	}
	ul.social-info.info-list li label {
		min-width: 65px;
	}
	/*---Manage Listing-----*/
	.small-list-img {
		max-width: 80px;
		padding: 5px;
		float: none;
		margin-right: 0;
		margin: 10px auto;
	}
	.small-list-img img {
		border-radius: 50%;
	}
	.small-list-detail {
		display: block;
		text-align: center;
	}
	.small-list-action {
		float: none;
		margin-right: 15px;
		padding-top: 15px;
		text-align: center;
		margin-bottom: 15px;
	}
	.single-comment .single-comment .img-holder {
    position: relative;
    margin-bottom: 10px;
	}
	.single-comment .single-comment {
		padding-left: 0;
	}
	
}

@media screen and (max-width: 479px) {
	.heading h2 {
		font-size: 25px;
	}

}