<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['currentStep', 'lastStep', 'showSubmit' => false]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['currentStep', 'lastStep', 'showSubmit' => false]); ?>
<?php foreach (array_filter((['currentStep', 'lastStep', 'showSubmit' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="d-flex justify-content-between my-4 <?php echo e($currentStep == $currentStep ? '' : 'd-none'); ?>"">
    <!--[if BLOCK]><![endif]--><?php if($currentStep > 0): ?>
        <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
            Retour
        </button>
    <?php else: ?>
        <div></div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    
    <!--[if BLOCK]><![endif]--><?php if($currentStep < $lastStep): ?>
        <button type="button" class="btn theme-btn" wire:click="nextStep">
            Continuer
        </button>
    <?php elseif($showSubmit): ?>
        <button id="submit-btn" class="btn theme-btn" type="submit" wire:loading.attr='disabled'>
            <i class="fa fa-save fa-lg"></i>
            Enregistrer
        </button>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/components/admin/step-navigation.blade.php ENDPATH**/ ?>