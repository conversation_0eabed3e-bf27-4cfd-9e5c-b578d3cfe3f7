{{-- 
    Reusable component for admin creation forms with maps and select2
    Usage: <x-admin.creation-form-scripts />
    
    Optional props:
    - mapId: ID of the map element (default: 'map')
    - center: Map center coordinates (default: Togo coordinates)
    - zoom: Initial zoom level (default: 6)
    - clickable: Whether map is clickable (default: true)
    - withSelect2: Include Select2 initialization (default: true)
    - withStepRefresh: Include step change refresh (default: true)
--}}

@props([
    'mapId' => 'map',
    'center' => ['lat' => 8.6195, 'lng' => 0.8248],
    'zoom' => 6,
    'clickable' => true,
    'withSelect2' => true,
    'withStepRefresh' => true
])

@push('scripts')
<script>
    var mymap;
    var marker;
    var mapHelper;

    document.addEventListener('livewire:initialized', function () {
        console.log('Initializing admin creation form scripts...');
        
        // Initialize Google Maps using global system
        mapHelper = initGoogleMap('{{ $mapId }}', {
            center: { lat: {{ $center['lat'] }}, lng: {{ $center['lng'] }} },
            zoom: {{ $zoom }},
            clickable: {{ $clickable ? 'true' : 'false' }}
        }, function(helper, map) {
            mymap = map;
            marker = helper.marker;
            console.log('Admin creation form map initialized');

            @if($withStepRefresh)
            // Refresh map when step changes
            Livewire.on('stepChanged', () => {
                console.log('Step changed, refreshing map...');
                setTimeout(() => {
                    helper.refresh();
                }, 100);
            });
            @endif
        });
    });

    // @if($withSelect2)
    // $(document).ready(function() {
    //     console.log('Initializing Select2 for admin creation form...');
        
    //     $('.select2').select2({
    //         height: '25px',
    //         width: '100%',
    //     });

    //     $('.select2').on('change', function(e) {
    //         var data = $(this).val();
    //         var nom = $(this).data('nom');
    //         console.log('Select2 changed:', nom, '=', data);
    //         @this.set(nom, data);
    //     });
    // });
    // @endif
</script>
@endpush
