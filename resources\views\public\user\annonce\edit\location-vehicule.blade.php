@extends('layout.public.app')

@section('title', 'Modifier une Location de Véhicule')

@section('content')


    @php
        $breadcrumbs = [
            ['route' => 'accueil', 'label' => 'Accueil'],
            ['route' => 'public.annonces.list', 'label' => 'Mes annonces'],
            ['label' => 'Location de véhicule'],
        ];
    @endphp

    <x-breadcumb backgroundImage="{{ asset('assets_client/img/banner/image-2.jpg') }}" :showTitle="true"
        title="Modifier un véhicule" :breadcrumbs="$breadcrumbs" />

    <div class="page-name row">
        <div class="container text-left p-0">
            @livewire('admin.location-vehicule.edit', ['locationVehicule' => $locationVehicule])
        </div>
    </div>
@endsection

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.full.min.js"></script>
    <script>
        var mymap;
        var marker;
        var mapHelper;

        var lat = {{ $locationVehicule->annonce->latitude }};
        var lng = {{ $locationVehicule->annonce->longitude }};

        // Initialize Google Maps using global system
        mapHelper = initGoogleMap('map', {
            center: { lat: lat, lng: lng },
            zoom: 8,
            clickable: true
        }, function(helper, map) {
            mymap = map;
            // Set marker at existing location
            helper.setMarker(lat, lng, 8);
            marker = helper.marker;
        });
    </script>

    <script>
        $('.select2').each(function() {
            $(this).select2({
                theme: 'bootstrap-5',
                dropdownParent: $(this).parent(),
            });
        });
    </script>
@endsection
