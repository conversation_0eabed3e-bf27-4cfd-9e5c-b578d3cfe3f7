<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreReferenceValeurRequest;
use App\Http\Requests\UpdateReferenceValeurRequest;
use App\Models\ReferenceValeur;

class ReferenceValeurController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreReferenceValeurRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ReferenceValeur $referenceValeur)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ReferenceValeur $referenceValeur)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateReferenceValeurRequest $request, ReferenceValeur $referenceValeur)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReferenceValeur $referenceValeur)
    {
        //
    }
}
