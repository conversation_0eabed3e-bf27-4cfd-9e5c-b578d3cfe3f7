<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['pays', 'villes', 'quartiers','longitude', 'latitude']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['pays', 'villes', 'quartiers','longitude', 'latitude']); ?>
<?php foreach (array_filter((['pays', 'villes', 'quartiers','longitude', 'latitude']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div>

    <div class="row align-items-start">
        <div class="col-md-4 col-sm-12 p-0">
            <div class="col">
                <h3 class="">Pays <b style="color: red; font-size: 100%;">*</b></h3>
                
                <select class="form-control" data-nom="pays_id" wire:model.lazy='pays_id' required>
                    <option value="">-- Sélectionner --</option>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $pays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($p->id); ?>"><?php echo e($p->nom); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </select>
            </div>
        </div>

        <div class="col-md-4 col-sm-12 p-0">
            <div class="col">
                <h3 class="">Ville <b style="color: red; font-size: 100%;">*</b></h3>
                
                <select class="form-control" data-nom="ville_id" wire:model.lazy='ville_id' required>
                    <option value="">-- Sélectionner --</option>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $villes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($v->id); ?>"><?php echo e($v->nom); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </select>
            </div>
        </div>

        <div class="col-md-4 col-sm-12 p-0">
            <div class="col">
                <h3>Quartier <b style="color: red; font-size: 100%;">*</b></h3>
                <input class="form-control" data-nom="quartier_id" type="text" wire:model='quartier_id'
                    placeholder="Saissisez le quartier" list="quartiers-list" required>
                <datalist id="quartiers-list">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $quartiers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $q): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($q->nom); ?>"></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </datalist>
            </div>
        </div>
    </div>

<div class="row align-items-start m-0 p-2">
    <div class="row">
        <div class="col-md-12 col-sm-12" style="margin-top: 10px;">
            <button class="btn btn-sm btn-success" type="button" id="locateBtn">
                <i class="fa fa-location-arrow"></i> Me localiser
            </button>
        </div>
    </div>

    <div class="row" wire:ignore>
        <div class="col-md-12 col-sm-12" style="margin-top: 10px;">
            <div id="map" style="width: 100%; height: 400px; z-index: 1;"></div>
        </div>
    </div>

    <!-- Hidden fields synced with Livewire -->
    <input type="hidden" id="latitude" wire:model="latitude">
    <input type="hidden" id="longitude" wire:model="longitude">

    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['longitude'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <span class="text-center text-danger pt-3"><?php echo e($message); ?></span>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
</div>
</div>


<?php if (isset($component)) { $__componentOriginal68ae95b500761c8e20cbd39dee90f904 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68ae95b500761c8e20cbd39dee90f904 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.public.maps-scripts','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('public.maps-scripts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68ae95b500761c8e20cbd39dee90f904)): ?>
<?php $attributes = $__attributesOriginal68ae95b500761c8e20cbd39dee90f904; ?>
<?php unset($__attributesOriginal68ae95b500761c8e20cbd39dee90f904); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68ae95b500761c8e20cbd39dee90f904)): ?>
<?php $component = $__componentOriginal68ae95b500761c8e20cbd39dee90f904; ?>
<?php unset($__componentOriginal68ae95b500761c8e20cbd39dee90f904); ?>
<?php endif; ?>

<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/admin/annonce/location-template.blade.php ENDPATH**/ ?>