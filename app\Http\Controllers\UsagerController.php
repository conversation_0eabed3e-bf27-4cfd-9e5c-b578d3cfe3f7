<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUsagerRequest;
use App\Http\Requests\UpdateUsagerRequest;
use App\Models\Usager;

class UsagerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUsagerRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Usager $usager)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Usager $usager)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUsagerRequest $request, Usager $usager)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Usager $usager)
    {
        //
    }
}
