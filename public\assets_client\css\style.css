/*-----------------------------------------
* Theme Name: Listing Hub
* Author: Themez Hub
* Version: 2
* Last Change: Oct 7 2019
  Author URI    : http://www.themezhub.com/
-------------------------------------------------------------------*/
/*------------------------------------------------------------------
# Font Links
# general setting
	General: Heading
	General: Form and Button
	General: Pagination
# Global Settings
	Global Settings: Custom Button
	Global Settings: Padding Style
	Global Settings: Margin Style
	Global Settings: Background Style
	Global Settings: Transparent Background Style
	Global Settings: Transparent Background with border Style
	Global Settings: Color Style
	Global Settings: Border Color Style
	Global Settings: Status BG Style
	Global Settings: Height
	Global Settings: Width Style
	Global Settings: Line Height
	Global Settings: Font Size
	Global Settings: Label Background
	Global Settings: Custom checkbox
	Global Setting: Multiple Bg Color For Category
	Global Border Styles
	Global Settings: Listing & Blockquote Style
# Navigation
	Default Navigation
	Transparent Navigation

# Home Banner Styles
	Common Banner Style
	Home 1 Banner
	Home 2 Banner With Map
	Advance Search Filter
	Home 5 Banner
	Banner Search form style
# listing design
	Listing In Grid Style
	Liting Verticle Style
# listing Category Style
	listing Category Style 1
	listing Category Style 2
	listing Category Style 3

# Popular Cities Style
# Testimonial Styles
	Testimonial Styles 1
	Testimonial Styles 2

# Pricing Table Style
# Services Style
# Features Style
# Counter Section Style
# Tag Section Style
# Profile Page Styles
# Page Title Settings

# Component Style
	Component: Accordion 1
	Component: Accordion 2
	Component: Tab
	Component: Tab Style 1
	Component: Tab Style 2
	Component: Notify Style
	Component: Alert Style

# Footer Styles

# Dropper Settings

# Slick Slider

# Pages Settings
	Add listing
	Edit Wraper
	preview Listing
	Dropzone Settings
	Review Box
	payment Options
	Invoice Page
	Booking Confirmation
	Error Page Styles
	Filter Option
	TranslateY
	Manage Listing

# Sidebar Style
	Opening hour
	Listing In Sidebar
	Gallery Style
	Pages Settings

# Login and Signup Popup
# Blog Page Style
	Blog Style In Grid
	Blog Detail
	Blogs Comment Styl
	Blog Sidebar
# Icons Style
# Login And SignUp Style
# Bottom To top Scroll
-----------------------------------------------*/


/* ==========================================================================
Font Links
========================================================================== */

@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700,800&display=swap');
@import url('https://fonts.googleapis.com/css?family=Muli:400,600,700,800');
@import url('https://fonts.googleapis.com/css?family=Crimson+Text:400,600');
@import url('https://fonts.googleapis.com/css?family=Nunito:400,600,700');

/* ==========================================================================
general setting
========================================================================== */
/*------ General: Common Style ---------*/
html,
body {
    width: 100%;
    height: auto;
    margin: 0;
    padding: 0;
}

body {
    background: #ffffff;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 400;
    position: relative;
    color: #626a70;
    line-height: 24px;
}

.woocommerce-page,
.page-404,
body.archive,
body.page-template-page-listing,
body.blog,
body.single-product,
body.single-post {
    background: #f6f6f6;
}

p {
    position: relative;
    /* text-transform: capitalize; */
    line-height: 1.8;
    -webkit-transition: .2s ease-in;
    -moz-transition: .2s ease-in;
    transition: .2s ease-in;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 500;
    margin-bottom: .25em;
    margin-top: 0;
    font-family: 'Ysabeau', serif;
    color: #26354e;
}

.h5,
h5 {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: normal;
}

a,
a.navlink {
    color: inherit;
    outline: none;
}

a:active,
a:focus,
a:hover {
    color: #007a7a;
    text-decoration: none;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.badge {
    font-weight: 600;
}

.text-center {
    text-align: center;
}

section {
    padding: 10px 0 0 0;
}

.small-pad {
    padding: 1em 0 1.5em 0;
}

.min-pad {
    padding: 0px 7px;
}

section.gray {
    background: #eff2f5;
}

.light-gray {
    background: #F5F7F8;
}

.light-bg {
    background: #ffffff;
}

a.btn.call-btn {
    background: #ffffff;
    border-radius: 4px;
    padding: 0.8em 2em;
    color: #de6600;
    /* text-transform: capitalize; */
}

.d-block {
    display: block;
    margin-bottom: 10px;
}

.no-shadow,
.no-shadow:hover,
.no-shadow:focus {
    box-shadow: none !important;
}

.overlap {
    /*	z-index:222; */
    position: relative;
}

/*----------- General: Heading ------------*/
.heading {
    padding: 0px 0 20px 0;
    margin-bottom: 0px;
    text-align: left;
}

.heading h2 {
    font-weight: 600;
    margin-top: 0;

}

.heading.light h2,
.heading.light p {
    color: #ffffff;
}

.heading h2 span {
    color: #de6600;
}

.heading p {
    line-height: 1.7;
    font-size: 15px;
}

/*--------- General: Form and Button ------------*/
.page-name.restaurant button.btn.btn-danger.mb-2 {
    margin-left: auto;
}

.page-name.restaurant .row.align-items-end {
    margin-top: 3rem;
}

button:hover,
input:hover,
input:focus,
button:focus {
    outline: none;
}

.btn {
    border-radius: 32px;
    -webkit-box-shadow: none;
    width: auto;
    box-shadow: none;
    font-weight: 400;
    position: relative;
    border: 1px solid;
    background-image: none;
    padding: 1.5rem 2rem;
    transition: all ease 0.4s;
}

.theme-bg:hover {
    background: #de6600;
    color: white;
}

.btn-midium {
    padding: 15px 25px;
}

.btn-midium {
    padding: 15px 25px;
}

.btn-square {
    width: 44px;
    height: 42px;
    display: inline-block;
    text-align: center;
    line-height: 1;
    font-size: 16px;
    border-radius: 2px;
    margin: 5px;
    padding: 10px 15px;
    transition: all ease 0.4s;
}

.btn-square-large {
    width: 55px;
    height: 55px;
    display: inline-block;
    text-align: center;
    line-height: 55px;
    font-size: 18px;
    border-radius: 2px;
    margin: 7px;
}

.light-gray-btn {
    background: #e8edf1;
    border: 1px solid #e5eaef;
}

.light-gray-btn:hover,
.light-gray-btn:focus {
    color: #ffffff;
    background: #78909C;
    border: 1px solid #78909C;
}

.btn-general-white-bg {
    background: #ffffff;
    color: #de6600;
    border-color: #ffffff;
}

.btn-general-white-bg:hover,
.btn-general-white-bg:focus {
    background: #de6600;
    color: #ffffff;
    border-color: #de6600;
}

.btn-general-theme-bg {
    background: #de6600;
    color: #ffffff;
    border-color: #de6600;
}

.btn-general-theme-bg:hover,
.btn-general-theme-bg:focus {
    background: #ffffff;
    color: #de6600;
    border-color: #ffffff;
}

.btn-general-theme-trans-bg {
    background: rgba(255, 58, 114, 0.1);
    border-color: #de6600;
    color: #de6600;
}

.btn-general-theme-trans-bg:hover,
.btn-general-theme-trans-bg:focus {
    background: #de6600;
    border-color: #de6600;
    color: #ffffff;
}

.full-width {
    width: 100%;
}

.btn-width-200 {
    width: 200px;
    margin-left: auto;
    margin-right: auto;
}

.btn-radius {
    border-radius: 50px;
}

.form-control,
.form-control-plaintext,
.btn.btn-form,
.form-select,
.select2-container {
    height: 50px;
    border: 1px solid #dde6ef;
    margin-bottom: 10px;
    box-shadow: none;
    border-radius: 0;
    background: #fbfdff;
    font-size: 15px;
    color: #445461;
    font-weight: 400;
    width: 100%;
    text-align: left;
    text-transform: none;
    line-height: 1;
    padding: 15px;
}

.select2-container {
    height: auto;
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    display: inline-block !important;
    min-width: 250px;
    background-color: transparent !important;
    border: 0 !important;
    border-radius: 0 !important;
}

dropdown-wrapper:after {
    content: "\f078";
    font-family: 'FontAwesome';
    font-weight: 900;
    float: right;
    position: absolute;
    right: 10px;
}

li.select2-selection__choice {
    display: inline-block;
    margin-right: 8px;
    border: 1px solid #007a7a;
    border-radius: 32px;
    color: #fff;
    padding: 1rem;
    background: #007a7a;
    font-size: 16px;
    margin-bottom: 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    position: relative !important;
    border-right: 1px solid #fff !important;
    color: #fff !important;
    font-size: 1em !important;
    font-weight: normal !important;
    padding-right: 6px !important;
    margin-right: 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #007a7a !important;
    border: 0 !important;
    border-radius: 32px !important;
    padding: 1rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    display: none;
}

.bootstrap-select.form-control {
    margin-bottom: 0;
    padding: 0;
    margin-bottom: 10px;
    border: 1px solid #dde6ef;
}

.form-control:hover,
.form-control:focus {
    border: 1px solid #de6600;
    -webkit-box-shadow: 0 1px 1px rgba(7, 177, 7, .075);
    box-shadow: 0 1px 1px rgba(7, 177, 7, .075);
    outline: none;
}

.form-control .btn.dropdown-toggle.btn-default:hover,
.form-control .btn.dropdown-toggle.btn-default:focus {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
}

:not(.btn-check)+.btn:hover,
.btn:first-child:hover,
.btn:focus-visible,
.btn:hover {
    box-shadow: none;
    border: 1px solid #445461;
}

/*------------col-------------*/
.select2-container:not(.select2-container--below) {
    height: 50px;
}

body>.select2-container.select2-container--default.select2-container--open {
    background: transparent;
    border: transparent;
    max-width: 0;
}

.form-control,
.form-control-plaintext,
.btn.btn-form,
.form-select,
.select2-container {
    height: 50px;
    border: 1px solid #dde6ef;
    margin-bottom: 10px;
    box-shadow: none;
    border-radius: 0;
    background: #fbfdff;
    font-size: 15px;
    color: #445461;
    font-weight: 400;
    width: 100%;
    text-align: left;
    text-transform: none;
    line-height: 1;
    padding: 15px;
}

.select2-container {
    height: auto;
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    display: inline-block !important;
    min-width: 250px;
    background-color: transparent !important;
    border: 0 !important;
    border-radius: 0 !important;
}

dropdown-wrapper:after {
    content: "\f078";
    font-family: 'FontAwesome';
    font-weight: 900;
    float: right;
    position: absolute;
    right: 10px;
}

li.select2-selection__choice {
    display: inline-block;
    margin-right: 8px;
    border: 1px solid #007a7a;
    border-radius: 32px;
    color: #fff;
    padding: 1rem;
    background: #007a7a;
    font-size: 16px;
    margin-bottom: 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    position: relative !important;
    border-right: 1px solid #fff !important;
    color: #fff !important;
    font-size: 1em !important;
    font-weight: normal !important;
    padding-right: 6px !important;
    margin-right: 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #007a7a !important;
    border: 0 !important;
    border-radius: 32px !important;
    padding: 1rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    display: none;
}

.form-control,
.form-control-plaintext,
.btn.btn-form,
.form-select,
.select2-container {
    height: 50px;
    border: 1px solid #dde6ef;
    margin-bottom: 10px;
    box-shadow: none;
    border-radius: 3px;
    background: #fbfdff;
    font-size: 15px;
    color: #445461;
    font-weight: 400;
    width: 100%;
    text-align: left;
    text-transform: none;
    line-height: 1;
    padding: 15px;
}

.select2-container {
    height: auto;
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    display: inline-block !important;
    min-width: 250px;
    background-color: transparent !important;
    border: 0 !important;
    border-radius: 0 !important;
}

.dropdown-wrapper:after {
    content: "\f078";
    font-family: 'FontAwesome';
    font-weight: 900;
    float: right;
    position: absolute;
    right: 10px;
}

li.select2-selection__choice {
    display: inline-block;
    margin-right: 8px;
    border: 1px solid #007a7a;
    border-radius: 32px;
    color: #fff;
    padding: 1rem;
    background: #007a7a;
    font-size: 16px;
    margin-bottom: 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    position: relative !important;
    border-right: 1px solid #fff !important;
    color: #fff !important;
    font-size: 1em !important;
    font-weight: normal !important;
    padding-right: 6px !important;
    margin-right: 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #007a7a !important;
    border: 0 !important;
    border-radius: 32px !important;
    padding: 1rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    display: none;
}

.row.align-items-start {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 2rem;
    padding-top: 2rem;
}

.col {
    padding: 1rem 3rem;
}

.col h4 {
    font-family: 'Poppins', sans-serif;
    font-weight: normal;
    font-size: 14px;
    opacity: 0.5;
    padding: 1rem 0
}

.btn-form i.fa.fa-pencil {
    margin-left: 10px;
    opacity: 0.5;
}

/*---------------------*/

span.input-group-addon {
    color: #8995a2;
    border-color: #dde6ef;
    background: #fbfdff;
    border-left: 0;
}

nav.navbar.shadow.on.menu-center.no-full {
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.05);
}

.bootstrap-select button.btn.dropdown-toggle.bs-placeholder.btn-default {
    background: transparent;
    height: 46px;
    border: 1px solid transparent;
    color: #445461;
    text-shadow: none;
    border-radius: 0px;
    box-shadow: none;
}

.btn.btn-primary {
    border: 1px solid #de6600;
    border-radius: 0px;
    /* width: 100%;*/
    height: 46px;
    background: #de6600;
    /* text-transform: capitalize; */
    font-size: 16px;
}

.btn.btn-primary:hover,
.btn.btn-primary:focus {
    border: 1px solid #de6600;
    border-radius: 0px;
    width: 100%;
    height: 46px;
    background: #de6600;
    /* text-transform: capitalize; */
    font-size: 16px;
}

.btn-default,
.btn-default.active.focus,
.btn-default.active:focus,
.btn-default.active:hover,
.btn-default:active.focus,
.btn-default:active:focus,
.btn-default:active:hover,
.open>.dropdown-toggle.btn-default.focus,
.open>.dropdown-toggle.btn-default:focus,
.open>.dropdown-toggle.btn-default:hover {
    color: #007a7a;
    background-color: #fff;
    border: 1px solid #007a7a;
    height: 46px;
}

.bootstrap-select .dropdown-toggle:focus {
    outline: none !important;
    outline: none !important;
    outline-offset: 0 !important;
}

.bootstrap-select.btn-group .dropdown-menu li a {
    padding: 8px 10px;
}

.bootstrap-select.btn-group .dropdown-menu li a:hover {
    box-shadow: none;
    background: #de6600;
    color: #ffffff;
}

.btn-group.open .dropdown-toggle {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.btn-default.active,
.btn-default:active,
.open>.dropdown-toggle.btn-default {
    color: #445461;
    background-color: transparent;
    border-color: transparent;
}

button.btn.dropdown-toggle.btn-default {
    background: transparent;
    border: none;
    box-shadow: none;
    height: 46px;
}

.dropdown-menu>.active>a,
.dropdown-menu>.active>a:hover,
.dropdown-menu>.active>a:focus {
    background-color: #de6600;
}

/*------ Data Overlay & Bg ----*/
.bg-image {
    background-size: cover !important;
    background-position: center !important;
}

[data-overlay] {
    position: relative;
}

[data-overlay]:before {
    position: absolute;
    content: '';
    background:
        /*#182b54*/
        #007a7a;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

[data-overlay="1"]:before {
    opacity: 0.1;
}

[data-overlay="2"]:before {
    opacity: 0.2;
}

[data-overlay="3"]:before {
    opacity: 0.3;
}

[data-overlay="4"]:before {
    opacity: 0.4;
}

[data-overlay="5"]:before {
    opacity: 0.5;
}

[data-overlay="6"]:before {
    opacity: 0.6;
}

[data-overlay="7"]:before {
    opacity: 0.7;
}

[data-overlay="8"]:before {
    opacity: 0.8;
}

[data-overlay="9"]:before {
    opacity: 0.9;
}

[data-overlay="10"]:before {
    opacity: 1;
}

[data-overlay="0"]:before {
    opacity: 0;
}

/*------ Choosen Select Box ----*/
.chosen-container-single .chosen-single {
    background: #fbfdff;
    border: 1px solid #dde6ef;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #445461;
    height: 50px;
    line-height: 50px;
    margin-bottom: 10px;
}

.chosen-container-single .chosen-single div {
    top: 8px;
}

.chosen-container-active.chosen-with-drop .chosen-single {
    background-color: #fff;
    border: 1px solid #dde6ef;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transition: border linear 0.2s, box-shadow linear 0.2s;
    -o-transition: border linear 0.2s, box-shadow linear 0.2s;
    transition: border linear 0.2s, box-shadow linear 0.2s;
}

.chosen-container-single .chosen-search input[type="text"] {
    border: 1px solid #dde6ef;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 1px 0;
    padding: 4px 20px 4px 4px;
    width: 100%;
}

.chosen-container .chosen-results li.highlighted {
    background-color: #f4f5f7;
    background-image: none;
    color: #445661;
}

.chosen-container-active.chosen-with-drop .chosen-single div b {
    background-position: -15px 7px;
}

.chosen-container .chosen-drop {
    background: #fff;
    border: 1px solid #dde6ef;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
    margin-top: -1px;
    position: absolute;
    top: 100%;
    left: -9000px;
    z-index: 1060;
}

/*---------- General: Pagination -------------*/
.pagination {
    display: table;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 4px;
    margin: 20px auto;
}

.pagination>li>a,
.pagination>li>span {
    position: relative;
    float: left;
    padding: 0;
    margin: 5px;
    line-height: 1.42857143;
    color: #5a6f7c;
    text-decoration: none;
    background-color: #fff;
    border-radius: 50px;
    width: 37px;
    height: 37px;
    text-align: center;
    line-height: 37px;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
}

.pagination>.active>a,
.pagination>.active>span,
.pagination>.active>a:hover,
.pagination>.active>span:hover,
.pagination>.active>a:focus,
.pagination>.active>span:focus,
.pagination>li>a:hover,
.pagination>li>a:focus {
    z-index: 2;
    color: #de6600;
    cursor: pointer;
    background-color: rgba(64, 65, 67, 0.1);
    border-color: #de6600;
}

.pagination li:first-child a {
    background: #de6600;
    border: 1px solid #de6600;
    border-radius: 50px;
    color: #ffffff;
}

.pagination li:last-child a {
    background: #35434e;
    border: 1px solid #35434e;
    border-radius: 50px;
    color: #ffffff;
}

/* ==========================================================================
    Global Settings
========================================================================== */
.theme-bg {
    background: #de6600;
    color: #ffffff;
}

.theme-bg p {
    color: #ffffff;
}

.dark-bg {
    background: #007a7a;
}

.light-bg {
    background: #ffffff;
}

.gray-bg {
    background: #f4f5f7;
}

.theme-cl {
    color: #de6600;
}

.theme-overlap {
    background: url(../img/slider-2.jpg);
    background-position: center !important;
    background-size: cover !important;
    position: relative;
}

.theme-overlap:before {
    background: #de6600;
}

.theme-overlap:before {
    opacity: 0.8;
    content: "";
    display: block;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    position: absolute;
}

/*---------- Global Settings: Custom Button -----------*/
.btn-radius {
    border-radius: 50px;
}

.theme-btn {
    background: #de6600;
    border: 1px solid #de6600;
    color: #ffffff;
}

.theme-btn:hover,
.theme-btn:focus {
    color: #ffffff;
    background: #de6600;
    border: 1px solid #de6600;
}

.btn.theme-btn-outlined,
a.theme-btn-outlined {
    background: transparent;
    border: 1px solid #de6600;
    color: #de6600;
}

.btn.theme-btn-outlined:hover,
a.theme-btn-outlined:hover,
.btn.theme-btn-outlined:focus,
a.theme-btn-outlined:focus {
    background: #de6600;
    border-color: #de6600;
    color: #ffffff;
}

.btn.theme-btn-trans-radius,
a.theme-btn-trans-radius {
    background: rgba(255, 58, 114, 0.1);
    color: #de6600;
    border-radius: 50px;
    border: 1px solid #de6600;
}

.btn.theme-btn-trans-radius:hover,
a.theme-btn-trans-radius:hover,
.btn.theme-btn-trans-radius:focus,
a.theme-btn-trans-radius:focus {
    background: #de6600;
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #de6600;
}

.btn.theme-btn-trans,
a.theme-btn-trans {
    background: rgba(255, 58, 114, 0.1);
    color: #de6600;
    border-radius: 2px;
    border: 1px solid #de6600;
}

.btn.theme-btn-trans:hover,
a.theme-btn-trans:hover,
.btn.theme-btn-trans:focus,
a.theme-btn-trans:focus {
    background: #de6600;
    color: #ffffff;
    border-radius: 2px;
    border: 1px solid #de6600;
}

.btn.btn-light-outlined,
a.btn-light-outlined {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #ffffff;
    color: #ffffff;
}

.btn.btn-light-outlined:hover,
a.btn-light-outlined:hover,
.btn.btn-light-outlined:focus,
a.btn-light-outlined:focus {
    background: rgba(255, 255, 255, 1);
    border: 1px solid #ffffff;
    color: #de6600;
}

.btn.light-btn,
a.light-btn {
    background: #ffffff;
    border: 1px solid #ffffff;
    color: #de6600;
}

.btn.light-btn:hover,
.btn.light-btn:focus,
a.light-btn:hover,
a.light-btn:focus {
    background: #de6600;
    border: 1px solid #de6600;
    color: #ffffff;
}

/*------- Global Settings: Padding Style ----------*/
/*
html body .padd-0{
	padding:0px;
}
html body .padd-5{
	padding:5px;
}
html body .padd-10{
	padding:10px;
}
html body .padd-15{
	padding:15px;
}
html body .padd-20{
	padding:20px;
}
html body .padd-l-0{
	padding-left:0px;
}
html body .padd-l-5{
	padding-left:5px;
}
html body .padd-l-10{
	padding-left:10px;
}
html body .padd-l-15{
	padding-left:15px;
}
html body .padd-r-0{
	padding-right:0px;
}
html body .padd-r-5{
	padding-right:5px;
}
html body .padd-r-10{
	padding-right:10px;
}
html body .padd-r-10{
	padding-right:15px;
}
html body .padd-top-0{
	padding-top:0px;
}
html body .padd-top-5{
	padding-top:5px;
}
html body .padd-top-10{
	padding-top:10px;
}
html body .padd-top-15{
	padding-top:15px;
}
html body .padd-top-20{
	padding-top:20px;
}
html body .padd-top-25{
	padding-top:25px;
}
html body .padd-top-30{
	padding-top:30px;
}
html body .padd-top-40{
	padding-top:40px;
}
html body .padd-bot-0{
	padding-bottom:0px;
}
html body .padd-bot-5{
	padding-bottom:5px;
}
html body .padd-bot-10{
	padding-bottom:10px;
}
html body .padd-bot-15{
	padding-bottom:15px;
}
html body .padd-bot-20{
	padding-bottom:20px;
}
html body .padd-bot-25{
	padding-bottom:25px;
}
html body .padd-bot-30{
	padding-bottom:30px;
}
html body .padd-bot-40{
	padding-bottom:40px;
}
*/
/*------- Global Settings: Margin Style ----------*/
html body .mrg-0 {
    margin: 0px;
}

html body .mrg-5 {
    margin: 5px;
}

html body .mrg-10 {
    margin: 10px;
}

html body .mrg-15 {
    margin: 15px;
}

html body .mrg-20 {
    margin: 20px;
}

html body .mrg-l-0 {
    margin-left: 0px;
}

html body .mrg-l-5 {
    margin-left: 5px;
}

html body .mrg-l-10 {
    margin-left: 10px;
}

html body .mrg-l-15 {
    margin-left: 15px;
}

html body .mrg-r-0 {
    margin-right: 0px;
}

html body .mrg-r-5 {
    margin-right: 5px;
}

html body .mrg-r-10 {
    margin-right: 10px;
}

html body .mrg-r-15 {
    margin-right: 15px;
}

html body .mrg-top-0 {
    margin-top: 0px;
}

html body .mrg-top-5 {
    margin-top: 5px;
}

html body .mrg-top-10 {
    margin-top: 10px;
}

html body .mrg-top-15 {
    margin-top: 15px;
}

html body .mrg-top-20 {
    margin-top: 20px;
}

html body .mrg-top-25 {
    margin-top: 25px;
}

html body .mrg-top-30 {
    margin-top: 30px;
}

html body .mrg-top-40 {
    margin-top: 40px;
}

html body .mrg-bot-0 {
    margin-bottom: 0px;
}

html body .mrg-bot-5 {
    margin-bottom: 5px;
}

html body .mrg-bot-10 {
    margin-bottom: 10px;
}

html body .mrg-bot-15 {
    margin-bottom: 15px;
}

html body .mrg-bot-20 {
    margin-bottom: 20px;
}

html body .mrg-bot-25 {
    margin-bottom: 25px;
}

html body .mrg-bot-30 {
    margin-bottom: 30px;
}

html body .mrg-bot-40 {
    margin-bottom: 40px;
}

html body .extra-mrg-5 {
    margin: 0 -5px;
}

html body .extra-mrg-10 {
    margin: 0 -10px;
}

html body .extra-mrg-15 {
    margin: 0 -15px;
}

html body .extra-mrg-20 {
    margin: 0 -20px;
}

/*------- Global Settings: Background Style ----------*/
html body .bg-info {
    background: #01b2ac;
}

html body .bg-primary {
    background: #1194f7;
}

html body .bg-danger {
    background: #f21136;
}

html body .bg-warning {
    background: #ff9800;
}

html body .bg-success {
    background: #0fb76b;
}

html body .bg-purple {
    background: #c580ff;
}

html body .bg-default {
    background: #283447;
}

/*------- Global Settings: Transparent Background Style ----------*/
html body .bg-trans-info {
    background: rgba(2, 182, 179, 0.12);
}

html body .bg-trans-primary {
    background: rgba(17, 148, 247, 0.12);
}

html body .bg-trans-danger {
    background: rgba(242, 17, 54, 0.12);
}

html body .bg-trans-warning {
    background: rgba(255, 152, 0, 0.12);
}

html body .bg-trans-success {
    background: rgba(15, 183, 107, 0.12);
}

html body .bg-trans-purple {
    background: rgba(197, 128, 255, 0.12);
}

html body .bg-trans-default {
    background: rgba(40, 52, 71, 0.12);
}

/*------- Global Settings: Transparent Background with border Style ----------*/
html body .bg-info-br {
    border: 1px solid #01b2ac;
    background: rgba(2, 182, 179, 0.12);
}

html body .bg-primary-br {
    border: 1px solid #1194f7;
    background: rgba(17, 148, 247, 0.12);
}

html body .bg-danger-br {
    border: 1px solid #f21136;
    background: rgba(242, 17, 54, 0.12);
}

html body .bg-warning-br {
    border: 1px solid #ff9800;
    background: rgba(255, 152, 0, 0.12);
}

html body .bg-success-br {
    border: 1px solid #0fb76b;
    background: rgba(15, 183, 107, 0.12);
}

html body .bg-purple-br {
    border: 1px solid #c580ff;
    background: rgba(197, 128, 255, 0.12);
}

html body .bg-default-br {
    border: 1px solid #283447;
    background: rgba(40, 52, 71, 0.12);
}

/*------- Global Settings: Color Style ----------*/
html body .cl-info {
    color: #01b2ac;
}

html body .cl-primary {
    color: #1194f7;
}

html body .cl-danger {
    color: #f21136;
}

html body .cl-theme {
    color: #de6600;
}

html body .cl-warning {
    color: #ff9800;
}

html body .cl-success {
    color: #0fb76b;
}

html body .cl-purple {
    color: #c580ff;
}

html body .cl-default {
    color: #283447;
}

html body .cl-light {
    color: #ffffff;
}

/*------- Global Settings: Border Color Style ----------*/
html body .br-info {
    border-color: #01b2ac;
}

html body .br-primary {
    border-color: #1194f7;
}

html body .br-danger {
    border-color: #f21136;
}

html body .br-warning {
    border-color: #ff9800;
}

html body .br-success {
    border-color: #0fb76b;
}

html body .br-purple {
    border-color: #c580ff;
}

html body .br-default {
    border-color: #283447;
}

/*------------ Global Settings: Status BG Style --------------*/
html body .bg-online {
    background: #68c70b;
}

html body .bg-offline {
    background: #e02b0d;
}

html body .bg-busy {
    background: #2196f3;
}

html body .bg-working {
    background: #ff9800;
}

/*---------- Global Settings: Height ----------*/
html body .normal-height {
    height: 46px;
}

html body .height-10 {
    height: 10px;
}

html body .height-20 {
    height: 20px;
}

html body .height-30 {
    height: 30px;
}

html body .height-40 {
    height: 40px;
}

html body .height-50 {
    height: 50px;
}

html body .height-60 {
    height: 60px;
}

html body .height-70 {
    height: 70px;
}

html body .height-80 {
    height: 80px;
}

html body .height-90 {
    height: 90px;
}

html body .height-100 {
    height: 100px;
}

html body .height-110 {
    height: 110px;
}

html body .height-120 {
    height: 120px;
}

html body .height-130 {
    height: 130px;
}

html body .height-140 {
    height: 140px;
}

html body .height-150 {
    height: 150px;
}

html body .height-160 {
    height: 160px;
}

html body .height-170 {
    height: 170px;
}

html body .height-180 {
    height: 180px;
}

html body .height-190 {
    height: 190px;
}

html body .height-200 {
    height: 200px;
}

html body .height-210 {
    height: 210px;
}

html body .height-220 {
    height: 220px;
}

html body .height-230 {
    height: 230px;
}

html body .height-240 {
    height: 240px;
}

html body .height-250 {
    height: 250px;
}

html body .height-260 {
    height: 260px;
}

html body .height-270 {
    height: 270px;
}

html body .height-280 {
    height: 280px;
}

html body .height-290 {
    height: 290px;
}

html body .height-300 {
    height: 300px;
}

html body .height-350 {
    height: 350px;
}

html body .height-400 {
    height: 400px;
}

html body .height-450 {
    height: 450px;
}

/*----------- Global Settings: Width Style -----------*/
html body .full-width {
    width: 100%;
}

html body .width-30 {
    width: 30px;
}

html body .width-40 {
    width: 40px;
}

html body .width-50 {
    width: 50px;
}

html body .width-60 {
    width: 60px;
}

html body .width-70 {
    width: 70px;
}

html body .width-80 {
    width: 80px;
}

html body .width-90 {
    width: 90px;
}

html body .width-100 {
    width: 100px;
}

html body .width-110 {
    width: 110px;
}

html body .width-120 {
    width: 20px;
}

html body .width-130 {
    width: 130px;
}

html body .width-140 {
    width: 140px;
}

html body .width-150 {
    width: 150px;
}

html body .width-160 {
    width: 160px;
}

html body .width-170 {
    width: 170px;
}

html body .width-180 {
    width: 180px;
}

html body .width-190 {
    width: 190px;
}

html body .width-200 {
    width: 200px;
}

html body .width-210 {
    width: 210px;
}

html body .width-220 {
    width: 220px;
}

html body .width-230 {
    width: 230px;
}

html body .width-240 {
    width: 240px;
}

html body .width-250 {
    width: 250px;
}

html body .width-260 {
    width: 260px;
}

html body .width-270 {
    width: 270px;
}

html body .width-280 {
    width: 280px;
}

html body .width-290 {
    width: 290px;
}

html body .width-300 {
    width: 300px;
}

/*---------- Global Settings: Line Height ---------*/
html body .line-height-10 {
    line-height: 10px;
}

html body .line-height-12 {
    line-height: 12px;
}

html body .line-height-14 {
    line-height: 14px;
}

html body .line-height-16 {
    line-height: 16px;
}

html body .line-height-18 {
    line-height: 18px;
}

html body .line-height-20 {
    line-height: 20px;
}

html body .line-height-22 {
    line-height: 22px;
}

html body .line-height-24 {
    line-height: 24px;
}

html body .line-height-26 {
    line-height: 26px;
}

html body .line-height-28 {
    line-height: 28px;
}

html body .line-height-30 {
    line-height: 30px;
}

html body .line-height-32 {
    line-height: 32px;
}

html body .line-height-34 {
    line-height: 34px;
}

html body .line-height-36 {
    line-height: 36px;
}

html body .line-height-38 {
    line-height: 38px;
}

html body .line-height-40 {
    line-height: 40px;
}

html body .line-height-42 {
    line-height: 42px;
}

html body .line-height-44 {
    line-height: 44px;
}

html body .line-height-46 {
    line-height: 46px;
}

html body .line-height-48 {
    line-height: 48px;
}

html body .line-height-50 {
    line-height: 50px;
}

html body .line-height-60 {
    line-height: 60px;
}

html body .line-height-70 {
    line-height: 70px;
}

html body .line-height-80 {
    line-height: 80px;
}

html body .line-height-90 {
    line-height: 90px;
}

html body .line-height-100 {
    line-height: 100px;
}

html body .line-height-110 {
    line-height: 110px;
}

html body .line-height-120 {
    line-height: 120px;
}

html body .line-height-130 {
    line-height: 130px;
}

html body .line-height-140 {
    line-height: 140px;
}

html body .line-height-150 {
    line-height: 150px;
}

html body .line-height-160 {
    line-height: 160px;
}

html body .line-height-170 {
    line-height: 170px;
}

html body .line-height-180 {
    line-height: 180px;
}

html body .line-height-190 {
    line-height: 190px;
}

html body .line-height-200 {
    line-height: 200px;
}

html body .line-height-210 {
    line-height: 210px;
}

html body .line-height-220 {
    line-height: 220px;
}

html body .line-height-230 {
    line-height: 230px;
}

html body .line-height-240 {
    line-height: 240px;
}

html body .line-height-250 {
    line-height: 250px;
}

html body .line-height-260 {
    line-height: 260px;
}

html body .line-height-270 {
    line-height: 270px;
}

html body .line-height-280 {
    line-height: 280px;
}

html body .line-height-290 {
    line-height: 290px;
}

html body .line-height-300 {
    line-height: 300px;
}

html body .line-height-350 {
    line-height: 350px;
}

html body .line-height-400 {
    line-height: 400px;
}

html body .line-height-450 {
    line-height: 450px;
}

/*---------- Global Settings: Font Size ----------*/
html body .font-10 {
    font-size: 10px;
}

html body .font-12 {
    font-size: 12px;
}

html body .font-13 {
    font-size: 13px;
}

html body .font-16 {
    font-size: 16px;
}

html body .font-18 {
    font-size: 18px;
}

html body .font-15 {
    font-size: 15px;
}

html body .font-20 {
    font-size: 20px;
}

html body .font-25 {
    font-size: 25px;
}

html body .font-30 {
    font-size: 30px;
}

html body .font-35 {
    font-size: 35px;
}

html body .font-40 {
    font-size: 40px;
}

html body .font-45 {
    font-size: 45px;
}

html body .font-50 {
    font-size: 50px;
}

html body .font-60 {
    font-size: 60px;
}

html body .font-70 {
    font-size: 70px;
}

html body .font-80 {
    font-size: 80px;
}

html body .font-90 {
    font-size: 90px;
}

html body .font-100 {
    font-size: 100px;
}

html body .font-bold {
    font-weight: bold;
}

html body .font-normal {
    font-weight: 400;
}

html body .font-midium {
    font-weight: 500;
}

html body .font-light {
    font-weight: 300;
}

html body .font-italic {
    font-style: italic;
}

/*---------- Global Settings: Label Background ----------*/
label {
    font-weight: normal;
}

label#hebergement,
label#beds {
    display: none;
}

html body .label-info {
    background: #01b2ac;
}

html body .label-primary {
    background: #1194f7;
}

html body .label-danger {
    background: #f21136;
}

html body .label-warning {
    background: #ff9800;
}

html body .label-success {
    background: #0fb76b;
}

html body .label-purple {
    background: #c580ff;
}

html body .label-default {
    background: #283447;
}

/*----------- Global Settings: Custom checkbox -----------*/
.custom-checkbox {
    position: relative;
}

.custom-checkbox input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    margin: 5px 0 0 3px;
    z-index: 9;
}

.custom-checkbox label:before {
    width: 18px;
    height: 18px;
}

.custom-checkbox label:before {
    content: '';
    margin-right: 10px;
    display: inline-block;
    vertical-align: text-top;
    background: #ffffff;
    border: 1px solid #bbb;
    border-radius: 2px;
    box-sizing: border-box;
    z-index: 2;
}

.custom-checkbox input[type="checkbox"]:checked+label:after {
    content: '';
    position: absolute;
    left: 6px;
    top: 5px;
    width: 6px;
    height: 11px;
    border: solid #000;
    border-width: 0 3px 3px 0;
    transform: inherit;
    z-index: 3;
    transform: rotateZ(45deg);
}

.custom-checkbox input[type="checkbox"]:checked+label:before {
    border-color: #de6600;
    background: #de6600;
}

.custom-checkbox input[type="checkbox"]:checked+label:after {
    border-color: #fff;
}

.custom-checkbox input[type="checkbox"]:disabled+label:before {
    color: #b8b8b8;
    cursor: auto;
    box-shadow: none;
    background: #ddd;
}

/*------------ Global Setting: Multiple Bg Color For Category ---------*/
html body .bg-a {
    background: #f73d51;
}

html body .bg-b {
    background: #8a7cd9;
}

html body .bg-c {
    background: #ffb390;
}

html body .bg-d {
    background: #37b475;
}

html body .bg-e {
    background: #4b5e6c;
}

html body .bg-f {
    background: #f5b83b;
}

html body .bg-g {
    background: #5565d0;
}

html body .bg-h {
    background: #18bad9;
}

html body .bg-i {
    background: #433c63;
}

html body .bg-j {
    background: #ad4f87;
}

html body .bg-k {
    background: #ee7d4e;
}

html body .bg-l {
    background: #ff465a;
}

html body .bg-m {
    background: #f5b83b;
}

html body .bg-o {
    background: #18bad9;
}

html body .bg-p {
    background: #6877de;
}

html body .bg-q {
    background: #14af69;
}

html body .bg-r {
    background: #576977;
    color: #576977;
}

html body .bg-s {
    background: #fd5c05;
}

html body .bg-t {
    background: #8a7cd9;
}

html body .bg-u {
    background: #ff465a;
}

html body .bg-v {
    background: #8a7cd9;
}

html body .bg-x {
    background: #18bad9;
}

html body .bg-y {
    background: #f5b83b;
}

html body .bg-z {
    background: #ff8645;
}

/*-------- Global Border Styles --------------*/
html body .border-0 {
    border: none;
}

html body .border-left {
    border-left: 1px solid #dde6ef;
}

html body .border-right {
    border-right: 1px solid #dde6ef;
}

html body .border-top {
    border-top: 1px solid #dde6ef;
}

html body .border-bottom {
    border-bottom: 1px solid #dde6ef;
}

html body .border-around {
    border: 1px solid #dde6ef;
}

/*---------- Global Settings: Listing & Blockquote Style ---------------*/
ol,
ul {
    margin-top: 0;
    margin-bottom: 10px;
    padding: 0;
}

ol.check-listing>li,
ul.check-listing>li {
    position: relative;
    letter-spacing: -0.2px;
    list-style: none;
}

ol.check-listing>li:before,
ul.check-listing>li:before {
    content: "\f00c";
    font-family: 'FontAwesome';
    font-size: 1em;
    margin-right: 7px;
    color: #de6600;
}

.card {
    border: none;
}

.quote-card {
    background: #fff;
    padding: 20px;
    padding-left: 50px;
    box-sizing: border-box;
    position: relative;
    min-height: 100px;
    border-left: none;
}

.quote-card p {
    font-size: 22px;
    line-height: 1.5;
    margin: 0;
    max-width: 90%;
}

.quote-card cite {
    font-size: 16px;
    margin-top: 10px;
    display: block;
    font-weight: 400;
    color: #de6600;
}

.quote-card:before {
    font-family: Georgia, serif;
    content: "“";
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 50px;
    color: #de6600;
    font-weight: normal;
}

/* ==========================================================================
    Navigation
========================================================================== */
.listing-price-info .categorytag {
    background: #de6600;
}
.pricetag {
    color: #ffffff !important;
    padding: 4px 18px;
    border-radius: 5px;
    font-size: 12px;
    /* right: 10px;*/
    color: #505667;
    box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.2);
    z-index: 0;
    text-align: center;
    white-space: nowrap; 
    width: 70px;
}
/*----------- Default Navigation -----------*/
.navbar {
    background-image: none;
    background-image: none;
    background-image: none;
    background-image: none;
    filter: none;
    background-repeat: repeat-x;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

nav.navbar.no-background .attr-nav>ul>li>a,
nav.navbar.navbar-transparent .attr-nav>ul>li>a,
nav.navbar.navbar-transparent ul.nav>li>a,
nav.navbar.no-background ul.nav>li>a {
    color: #677782;
    font-weight: 500;
    font-family: 'Nunito', sans-serif;

}

nav.navbar ul.nav>li>a {
    color: #677782;
    font-size: 15px;

    background-color: transparent !important;
    font-weight: 500;
    font-family: 'Nunito', sans-serif;
}

nav.navbar ul.nav ul.dropdown-menu>li>a {
    white-space: normal;
    font-weight: 400;
    opacity: 0.9;
}

nav.navbar ul.nav>li>a i {
    font-size: 16px;
    color: #8999a7;
    margin-right: 9px;
}

nav.navbar {
    background-color: #ffffff;
    border-bottom: none;
    z-index: 99999;
}

nav.navbar-transparent .navbar-toggler {
    border: 1px solid #fff;
}

nav.navbar-transparent .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

nav.navbar.bootsnav .navbar-collapse.collapse.show {
    display: block !important;
    background: #fff;
    margin-top: 15px;
}

.navbar>.container-fluid {
    justify-content: flex-start;
}

/*-------------user account------------------*/
.user-account ul {
    margin-bottom: 0;
}

.user-account li {
    list-style: none;
}

/*-------------------------------*/
body nav.navbar ul.nav>li>a.addlist i {
    color: #ffb8c1;
}

.nav>li>a>img.avater-img {
    max-width: 38px;
    float: left;
    vertical-align: middle;
    position: relative;
    top: -10px;
    margin-right: 10px;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.modal.fade .modal-dialog.modal-dialog {
    transform: translate(0);
}

/*---------- Transparent Navigation ---------*/
body.home-2 nav.navbar {
    background-color: #ffffff;
    border-bottom: none;
    -webkit-box-shadow: 0 2px 4px 0 rgba(188, 195, 208, 0.5);
    -moz-box-shadow: 0 2px 4px 0 rgba(188, 195, 208, 0.5);
    box-shadow: 0 2px 4px 0 rgba(188, 195, 208, 0.5);
    z-index: 999;
    padding: 2rem 3rem;
}

body.home-2 nav.navbar.bg-light.bootsnav.navbar-transparent {
    background-color: transparent !important;
}

.navbar.fixed-top {
    position: fixed !important;
}

@media (min-width: 768px) {
    .navbar-nav {
        float: none !important;
    }

    .navbar-nav>li>a {
        padding: 15px !important;
    }
}

@media (max-width: 992px) {
    body.home-2 nav.navbar.bg-light.bootsnav {
        padding: 10px 0;
    }

    .navbar-brand {
        margin-right: 15px !important;
    }

    .collapse.show form {
        padding: 15px 0;
    }

    .user-account {
        position: absolute;
        padding-left: 15px;
        right: 15px;
        z-index: 1;
        top: 18px;
    }

    .user-account .btn span,
    nav.navbar.bootsnav .user-account li.dropdown a.dropdown-toggle:before,
    .dropdown-toggle::after {
        display: none;
    }

    .user-account .btn-sm.dropdown-toggle {
        padding: 6px;
        border-radius: 40px;
        width: 35px;
        height: 35px;
    }

    .navbar-transparent .user-account .btn-sm.dropdown-toggle,
    .navbar-transparent .user-account .btn-sm.dropdown-toggle:hover,
    .navbar-transparent .user-account .btn-sm.dropdown-toggle:focus,
    .navbar-transparent .user-account .btn-sm.dropdown-toggle:active,
    .navbar-transparent .user-account .btn-sm.dropdown-toggle.focus,
    .navbar-transparent .user-account .btn-sm.dropdown-toggle.active,
    .navbar-transparent .user-account .btn-sm.dropdown-toggle.show {
        background: transparent;
        border-color: #fff;
        color: #fff;
    }

    .user-account .dropdown-menu[data-bs-popper] {
        top: 50px;
        left: auto;
        right: 0;
    }

    body.home-2 .user-account a.btn i {
        margin-right: 0;
    }
}

body.home-2 nav.navbar.navbar-transparent ul.nav>li>a.addlist {
    background: transparent;
}

body.home-2 nav.navbar ul.nav>li>a.addlist i {
    color: #ffffff;
}

body.home-2 nav.navbar.no-background .attr-nav>ul>li>a,
body.home-2 nav.navbar.navbar-transparent .attr-nav>ul>li>a,
body.home-2 nav.navbar.navbar-transparent ul.nav>li>a,
body.home-2 nav.navbar.no-background ul.nav>li>a {
    color: #ffffff;
    text-shadow: none;
}

nav form {
    margin-left: auto;
    width: auto;
    margin-right: 15px;
}

.nav-link {
    font-size: 16px;
}

nav.navbar.navbar-transparent .nav-link {
    color: #fff;
}

.btn.add-annonce {
    background: #fff;
    color: #007a7a;
    border-color: #007a7a;
}

.btn.add-annonce i {
    color: #007a7a;
}

/*--- Logo Style -----*/
nav.navbar .logo-display,
nav.navbar .logo-scrolled {
    max-height: 100px;
}

nav:not(.navbar-transparent) .logo-scrolled,
nav.navbar-transparent .logo-display {
    display: block;
    width: 100px;
    height: 100px;
    max-height: 100px;
    margin-top: -23px;
}

nav:not(.navbar-transparent) .logo-display,
nav.navbar-transparent .logo-scrolled {
    display: none !important;
}

.navbar-brand {
    font-family: 'Ysabeau', serif;
    color: #007a7a;
    font-size: 32px;
    padding: 0;
    margin-left: 0px;
}

.navbar-transparent .navbar-brand {
    color: #fff;
}

@media(max-width: 767px) {

    nav:not(.navbar-transparent) .logo-scrolled,
    nav.navbar-transparent .logo-display {
        display: block;
        width: 80px;
        height: 80px;
        max-height: 80px;
        margin-top: -14px;
        margin-right: 30px;
    }

    .navbar-brand span {
        display: none;
    }
}

/*-------- Dark Navbar -------*/
nav.navbar.dark {
    background-color: #1b2639;
}

nav.navbar.dark ul.nav>li>a {
    color: #95a7c5;
}

/*==========================================================================
       Home Banner Styles
========================================================================== */
/*--- Common Banner Style ------*/

@media(min-width: 768px) {
    .logo-home {
        width: 250px;
        height: 250px;
        background-size: contain;
        background-repeat: no-repeat;
        margin-left: 110px;
    }
}

form.form-verticle {
    width: 100%;
    display: inline-block;
    margin: 1em 0;
}

.form-box {
    position: relative;
    min-height: 52px;
}

.pulse-tag {
    width: 100%;
    display: block;
    margin: 2em auto 0 auto;
    text-align: center;
}

i.banner-icon {
    position: absolute;
    left: 15px;
    top: 16px;
    font-size: 16px;
    color: #90969e;
    z-index: 1;
}

a.pulse.btn-banner-link {
    width: 55px;
    height: 55px;
    display: inline-block;
    background: #ffffff;
    line-height: 55px;
    border-radius: 50%;
}

.pulse {
    z-index: 1;
    position: relative;
}

.pulse {
    box-shadow: 0 0 0 rgba(255, 255, 255, .55);
    animation: pulse 2s infinite;
}

@-webkit-keyframes pulse {
    0% {
        -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, .55)
    }

    70% {
        -webkit-box-shadow: 0 0 0 10px rgba(255, 255, 255, 0)
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0)
    }
}

@keyframes pulse {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, .55);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, .55)
    }

    70% {
        -moz-box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0)
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0)
    }
}

.pulse2 {
    box-shadow: 0 0 0 rgba(0, 26, 87, .3);
    animation: pulse2 2s infinite
}

@-webkit-keyframes pulse2 {
    0% {
        -webkit-box-shadow: 0 0 0 0 rgba(0, 26, 87, .3)
    }

    70% {
        -webkit-box-shadow: 0 0 0 10px rgba(0, 26, 87, 0)
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(0, 26, 87, 0)
    }
}

@keyframes pulse2 {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(0, 26, 87, .3);
        box-shadow: 0 0 0 0 rgba(0, 26, 87, .3)
    }

    70% {
        -moz-box-shadow: 0 0 0 10px rgba(0, 26, 87, 0);
        box-shadow: 0 0 0 10px rgba(0, 26, 87, 0)
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(0, 26, 87, 0);
        box-shadow: 0 0 0 0 rgba(0, 26, 87, 0)
    }
}

/*--------- Home 1 Banner ------------*/
.banner {
    background-position: center;
    background-attachment: fixed;
    background-size: cover;
    padding: 8% 0 7% 0;
    position: relative;
}

.banner.abn {
    padding: 6% 0 7% 0;
}

.banner-caption {
    text-align: left;
    display: inline-block;
    width: 100%;
}

.banner-text {
    /* margin-top: 3%; */
    margin-bottom: 1em;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.banner h1 {
    color: #ffffff;
    text-shadow: none;
    line-height: 1.3;
    letter-spacing: 3px;
    text-align: center;
}

.banner h2 {
    color: #ffffff;
    text-align: center;
}

.banner h4 {
    font-size: 20px;
    color: #ffffff;
    margin: 0;
    padding: 0;
    margin-bottom: 20px;
}

.banner h1 span {
    color: #de6600;
}

.banner p {
    color: #ffffff;
    opacity: 0.8;
    font-weight: 300;
    font-size: 20px;
    line-height: 1.8;
}

.banner .no-padd {
    padding: 0 0px;
}

.banner .btn {
    margin: 5px;
}

@media only screen and (min-width: 768px) {
    .banner-caption .form-control.left-radius {
        border-radius: 32px 0px 0px 32px !important;
    }

    .banner .btn.btn-default {
        border-radius: 0px 32px 32px 0px !important;
    }
}

/*---------- Home 2 With Map ------------*/
.home-map {
    position: relative;
}

#home-map {
    height: 700px;
    width: 100%;
}

.search-inner.abs-map-search .form-verticle {
    position: relative;
    display: table;
    bottom: 60px;
    top: auto;
    transform: none;
    padding: 40px 0 35px;
    z-index: 9999;
    margin-top: 0;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    border: 1px solid #eeeff3;
}

.search-inner form.form-verticle {
    background: #ffffff;
    padding-top: 18px;
    padding-bottom: 8px;
    border-radius: 6px;
}

.map-container {
    float: left;
    width: 100%;
    margin-top: 60px;
    position: relative;
    overflow: hidden;
}

.fw-map {
    height: 600px;
}

.map-container.column-map {
    width: 50%;
    position: fixed;
    -webkit-transform: translate3d(0, 0, 0);
    overflow: hidden;
}

.map-container.column-map.right-pos-map {
    right: 0;
}

.map-container.column-map.left-pos-map {
    left: 0;
}

.map-container #main-full-map {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 10;
    overflow: hidden;
}

#singleMap {
    width: 100%;
    position: relative;
    height: 350px;
    float: left;
    margin-bottom: 30px;
}

.box-widget #singleMap {
    margin-bottom: 10px;
}

.mapzoom-in,
.mapzoom-out {
    position: fixed;
    z-index: 100;
    top: 50%;
    text-align: center;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border: 3px solid #374558;
    border-radius: 100%;
    color: #fff;
    line-height: 40px;
    margin-top: -20px;
    background: #2a3646;
}

#singleMap .mapzoom-in,
#singleMap .mapzoom-out,
.home-map .mapzoom-in,
.home-map .mapzoom-out,
.fw-map .mapzoom-in,
.fw-map .mapzoom-out {
    position: absolute;
    right: 20px;
}

.map-container.column-map.right-pos-map .mapzoom-in,
.map-container.column-map.right-pos-map .mapzoom-out {
    right: 30px;
}

.map-container.column-map.left-pos-map .mapzoom-in,
.map-container.column-map.left-pos-map .mapzoom-out {
    left: 30px;
}

.mapzoom-in:before,
.mapzoom-out:before {
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-decoration: inherit;
    content: "\f068";
}

.mapzoom-in:before {
    content: "\f067";
}

.mapzoom-in {
    margin-top: -80px;
}

.mapnavigation {
    position: absolute;
    bottom: 50px;
    right: 7px;
    z-index: 30;
    width: 170px;
}

.mapnavigation a {
    width: 70px;
    padding: 8px 0;
    border-radius: 4px;
    color: #fff;
    float: left;
    margin-left: 10px;
    box-shadow: 0px 0px 0px 4px rgba(255, 255, 255, 0.4);
}

.mapnavigation a,
.mapzoom-in:hover,
.mapzoom-out:hover {
    background: #2F3B59;
}

.map-popup-box {
    display: block;
    width: 300px !important;
    position: relative;
}

.map-popup {
    display: block;
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0 9px 16px rgba(58, 87, 135, 0.15);
}

.map-popup-box:before {
    top: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    margin-top: -1px;
    z-index: 20;
}

.map-popup-box:before {
    border-color: transparent;
    border-top-color: #fff;
    border-width: 15px;
    margin-left: -15px;
}

.map-popup img {
    width: 100%;
    height: auto;
    -webkit-transition: all 2000ms cubic-bezier(.19, 1, .22, 1) 0ms;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    transition: all 2000ms cubic-bezier(.19, 1, .22, 1) 0ms;
}

.mp-img-box:hover img {
    -webkit-transform: scale(1.15);
    -moz-transform: scale(1.15);
    transform: scale(1.15);
}

.mp-list-content {
    background: #fff;
    padding: 25px 20px;
    z-index: 20;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.close-infobox {
    position: absolute;
    width: 30px;
    height: 30px;
    line-height: 30px;
    top: 20px;
    text-align: center;
    right: 20px;
    color: #121212;
    border-radius: 100%;
    z-index: 20;
    cursor: pointer;
    background: #ffffff;
}

.close-infobox:hover {
    background: #fff;
    color: #2a3646;
}

.mpl-title h4 {
    float: left;
    width: 100%;
    text-align: left;
    font-size: 16px;
    font-weight: 600;
    color: #566985;
    padding-bottom: 20px;
}

.mpl-title h4 a {
    color: #566985;
}

.mp-img-box {
    overflow: hidden;
}

.fl-wrap {
    float: left;
    width: 100%;
    position: relative;
}

.mp-img-box:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background: #292929;
    opacity: 0.3;
}

.cluster img {
    display: none
}

.cluster {
    width: 40px !important;
    height: 40px !important;
}

.cluster div {
    text-align: center;
    font-size: 15px !important;
    background: #2a3646;
    color: #fff !important;
    font-weight: 600;
    border-radius: 100%;
    width: 40px !important;
    height: 40px !important;
    line-height: 38px !important;
    box-shadow: 0px 0px 0px 4px rgba(0, 0, 0, 0.5);
    border: 2px solid #374558;
    -webkit-transition: all 300ms linear;
    transition: all 100ms 3inear;
    animation: cluster-animation 1.5s infinite;
}

.cluster div:hover {
    background: #5d6275;
}

@keyframes cluster-animation {

    0%,
    100% {
        box-shadow: 0px 0px 0px 4px rgba(0, 0, 0, 0.1);
    }

    50% {
        box-shadow: 0px 0px 0px 9px rgba(0, 0, 0, 0.1);
    }
}

.mp-store-call,
.mp-location-info {
    float: left;
    color: #667582;
    font-size: 14px;
    width: 100%;
    text-align: left;
    margin-bottom: 10px;
}

.mp-store-call i,
.mp-location-info i {
    padding-right: 10px;
    font-size: 14px;
}

.mp-ratting {
    position: absolute;
    z-index: 12;
    left: 20px;
    top: -30px;
}

.mp-ratting i {
    float: left;
    color: #FACC39;
    margin-top: -2px;
    margin-right: 6px;
}

.mp-review-count {
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    top: -4px;
}

.mp-category {
    position: absolute;
    top: 20px;
    left: 20px;
    font-weight: 500;
    color: #fff;
    z-index: 20;
    padding: 10px 12px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.17);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.side-full-map.map-container {
    height: 100vh;
    position: fixed !important;
    top: 0;
    padding-top: 60px;
    width: 50%;
    background-color: #fff;
    z-index: 50;
}

@media (max-width: 767px) {
    .side-full-map.map-container {
        width: 100%;
        position: relative !important;
        height: 500px;
    }
}

/*----- Advance Search Filter ------*/
.custom-radio {
    margin-right: 15px;
}

.custom-radio [type="radio"]:checked,
.custom-radio [type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}

.custom-radio [type="radio"]:checked+label,
.custom-radio [type="radio"]:not(:checked)+label {
    position: relative;
    padding-left: 22px;
    cursor: pointer;
    line-height: 20px;
    display: inline-block;
}

.custom-radio [type="radio"]:checked+label:before,
.custom-radio [type="radio"]:not(:checked)+label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 18px;
    height: 18px;
    border: 1px solid #ddd;
    border-radius: 100%;
    background: #fff;
}

.custom-radio [type="radio"]:checked+label:after,
.custom-radio [type="radio"]:not(:checked)+label:after {
    content: '';
    width: 12px;
    height: 12px;
    background: #ff431e;
    position: absolute;
    top: 3px;
    left: 3px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

.custom-radio [type="radio"]:not(:checked)+label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
}

.custom-radio [type="radio"]:checked+label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}

/*----Range Slider---*/
.slider-horizontal {
    width: 88% !important;
    margin-left: 12px;
}

.slider-horizontal .slider-handle-container,
.slider-horizontal .slider-track {
    height: 15px;
    margin-top: -5px;
    top: 50%;
    border-radius: 50px;
}

.range-slider .slider-selection {
    background: #ff431e;
}

.range-slider .slider-handle.round {
    background: white;
    top: 2px;
    border-radius: 50%;
    border: 2px solid #ff431e;
}

/*---------- Home 5 Banner ------------*/
body.home-2 .banner {
    background-position: center;
    background-attachment: fixed;
    background-size: cover;
}

body.home-2 .banner h1,
.banner h1 {
    font-size: 65px;
    opacity: 1;
    margin-bottom: 2px;
    padding-bottom: 0;
    font-weight: 600;
    text-align: left;
}

body.home-2 .banner p,
.banner p {
    color: #ffffff;
    margin: 0 auto 15px auto;
    font-weight: 300;
    line-height: 1.9;
    text-align: center;
    font-size: 16px;
}

body a.btn {
    padding: 10px 30px;
    transition: all ease 0.4s;
}

body.home-2 a.btn i {
    margin-right: 10px;
}

a.btn.contact-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

a.btn.contact-btn:hover,
a.btn.contact-btn:focus {
    background: #ffffff;
    border-color: #ffffff;
    color: #de6600;
}

a.btn.contact-btn:hover i,
a.btn.contact-btn:focus i {
    color: #de6600;
}

a.btn.listing-btn:hover,
a.btn.listing-btn:focus {
    background: #ffffff;
    border-color: #ffffff;
    color: #de6600;
}

a.btn.listing-btn:hover i,
a.btn.listing-btn:focus i {
    color: #de6600;
}

a.btn.listing-btn {
    background: #de6600;
    border: 1px solid #de6600;
    color: #ffffff;
}

.banner-info {
    padding: 4.5rem 0 1rem 0px;
    color: rgba(255, 255, 255, 1);
    text-align: center;
}

.banner-info i {
    display: block;
    margin-bottom: 10px;
    font-size: 25px;
    color: #ffffff;
}

/*-------- Banner Search form style ------------*/
.banner-caption .form-control {
    background: #ffffff;
    border: none;
    border-radius: 0px 0px 0px 0px;
    height: 52px;
    color: #90969e;
    font-size: 15px;
    width: 100%;
    box-shadow: none;
    padding-left: 40px;
}

.form-control .btn.dropdown-toggle.btn-default,
.form-control .btn.dropdown-toggle.btn-default:hover,
.form-control .btn.dropdown-toggle.btn-default:focus {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    font-size: 15px;
    color: #90969e;
}

.bs-searchbox input.form-control {
    height: 31px;
    border: 1px solid #e4e4e4;
    margin-bottom: 5px;
}

.banner .btn.btn-default {
    color: #ffffff;
    text-shadow: none;
    font-size: 20px;
    margin: 0;
    box-shadow: none;
    border-radius: 0px;
    padding: 0 20px;
    height: 52px;
    width: 100%;
}

.banner-caption .btn-primary {
    background: #de6600;
    height: 48px;
    color: #ffffff;
    border-radius: 0px;
    border-color: #de6600;
    width: 100%;
    font-size: 16px;
    text-transform: uppercase;
    display: block;
}

.banner-caption .btn-primary:focus {
    background: #de6600;
    color: #ffffff;
    border-color: #de6600;
}

.banner-caption .form-control::-moz-placeholder {
    color: #90969e;
    opacity: 1
}

.banner-caption .form-control:-ms-input-placeholder {
    color: #90969e
}

.banner-caption .form-control::-webkit-input-placeholder {
    color: #90969e
}


/*------------- New Popular Category on Banner -----------------*/
.popular-categories {
    margin-top: 30px;
    display: inline-block;
}

ul.popular-categories-list {
    display: table;
    margin: auto;
}

ul.popular-categories-list li {
    float: left;
    margin: 5px;
    list-style: none;
}

ul.popular-categories-list li a {
    background: rgba(255, 255, 255, 0.12);
    position: relative;
    width: 120px;
    height: 105px;
    display: block;
    overflow: hidden;
    color: #ffffff;
    border-radius: 4px;
    padding: 7px;
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out;
}

.pc-box {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    padding-top: 22px;
    box-shadow: 0px 0px 16px 0px rgba(2, 2, 2, 0.1);
    -webkit-box-shadow: 0px 0px 16px 0px rgba(2, 2, 2, 0.1);
}

.pc-box p {
    font-size: 13px;
    opacity: 1;
    font-weight: 500;
}

.pc-box i {
    font-size: 20px;
    margin-bottom: 10px;
}

ul.popular-categories-list li a:hover,
ul.popular-categories-list li a:focus {
    background: rgba(255, 255, 255, 0.17);
}

.list-detail h3 {
    font-size: 24px;
}

.list-detail h3 span {
    font-size: 20px;
}

span.pcat-name {
    position: absolute;
    top: 15px;
    left: 20px;
    background: rgba(255, 255, 255, 0.5);
    padding: 2px 15px;
    border-radius: 4px;
    color: #ffffff;
    font-size: 13px;
}

span.rating-count {
    font-size: 12px;
    margin-left: 3px;
}

.listing-info {
    float: right;
    display: block;
}

.listing-info ul {
    margin: 0;
    padding: 0;
}

.listing-info ul li {
    list-style: none;
    display: inline-block;
    padding: 0 8px;
    font-size: 13.5px;
}

.listing-info ul li i {
    margin-right: 6px;
}

span.like-listing {
    /* position: absolute;
    right: 20px;
    bottom: 20px; */
    display: table;
    #2d3248
}

.slick-track {
    padding: 10px 0;
}

.form-box {
    position: relative;
    min-height: 52px;
}

/*------------ Option Form-----------*/
.option-form {
    padding: 10px 0;
    background: #ffffff;
    border-radius: 4px;
}

.option-form .tab-content {
    padding: 0;
}

.option-form ul.nav.nav-tabs {
    border: none;
}

.option-form .nav-tabs>li.active>a,
.option-form .nav-tabs>li.active>a:focus,
.option-form .nav-tabs>li.active>a:hover {
    color: #ff431e;
    cursor: default;
    box-shadow: none;
    background-color: transparent;
    border: none;
    border-bottom-color: transparent;
}

.option-form .nav-tabs>li>a:hover {
    border-color: transparent;
    box-shadow: none;
    background: transparent;
    border-radius: 0;
}

.option-form .form-control {
    border: 1px solid #e4e4e4 !important;
    border-radius: 2px;
}

.banner .option-form .btn.btn-default {
    border-radius: 2px !important;
}

/*------------ Slider Banner --------------*/
.slide-text>h1 {
    font-weight: 600;
}

.slide-text>p {
    font-family: 'Lora', serif;
    font-style: italic;
}

/*==========================================
listing design
==========================================*/
/*----------- Listing In Grid Style ------------*/
.listing-shot {
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    overflow: hidden;
    margin-top: 25px;
    border-radius: 6px;
    background: #ffffff;
    padding: 0;
    display: inline-block;
    width: 100%;
    position: relative;
    transition: all ease 0.4s;
}

.bg-image .listing-shot {
    border: none;
}

.listing-shot:hover,
.listing-shot:focus {
    -webkit-box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
    -moz-box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
    box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
}

.listing-shot-img {
    position: relative;
}

.grid-style .listing-shot-img {
    max-height: 200px;
    height: 200px;
    overflow: hidden;
}

.listing-shot-img a img {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
}


a.like-listing {
    background: #de6600;
    border: 1px solid transparent;
    color: #ffffff;
    font-size: 11px;
    cursor: normal;
    border-radius: 30px;
    transition: all 0.4s;
    text-align: center;
    padding: 2px;
    width: 30px;
}

a.like-listing.alt {
    background: #ffffff;
    border: 1px solid #de6600;
    color: #de6600;
    font-size: 11px;
    cursor: normal;
    border-radius: 30px;
    transition: all 0.4s;
    text-align: center;
    padding: 2px;
    width: 30px;
}

.listing-shot-info.rating {
    /* border-top:1px solid rgba(71, 85, 95,0.11); */
    white-space: nowrap;
}

.listing-shot-caption,
.listing-shot-info {
    padding: 10px 15px;
    position: relative;
}

.listing-shot-caption h4 {
    margin-bottom: 12px;
    font-size: 20px;
}

p.listing-location {
    margin-bottom: 0;
    color: initial;
    font-size: 14px;
    opacity: 0.6;
}

.listing-detail-info span {
    display: block;
    color: #667582;
    margin-bottom: 7px;
}

.listing-detail-info span i {
    margin-right: 7px;
    color: #798a98;
    font-size: 15px;
}

.listing-shot-info.rating i {
    /* color: #a1acb5; */
}

.listing-shot-info.rating i.color {
    color: #FF9800;
}

.listing-shot-info.rating a.detail-link {
    color: #de6600;
    text-transform: uppercase;
    font-size: 16px;
    float: right;
    font-weight: 500;
}

.listing-badge.now-open {
    background-color: #54ba1d;
}

.listing-badge {
    background-color: #333;
    float: left;
    position: absolute;
    transform: rotate(-45deg);
    left: -60px;
    top: 30px;
    text-align: center;
    width: 200px;
    font-size: 12.5px;
    margin: 0;
    z-index: 999;
    color: #fff;
    font-weight: 500;
    line-height: 28px;
}

span.like-listing.style-2 {
    top: -20px;
}

span.listing-price {
    background: rgba(0, 0, 0, 0.17);
    color: #ffffff;
    position: absolute;
    bottom: 15px;
    left: 15px;
    padding: 1px 18px;
    letter-spacing: 1px;
    border-radius: 2px;
}

.featured-listing {
    position: absolute;
    left: 0;
    top: 40px;
    z-index: 1;
}

.featured-listing:before {
    content: "";
    border-top: 43px solid #ff8f00;
    border-right: 43px solid transparent;
}

.featured-listing:after {
    content: "\f005";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    left: 6px;
    top: -33px;
    color: #ffdd7f;
}

span.approve-listing {
    width: 25px;
    height: 25px;
    background: #10aa08;
    position: absolute;
    top: 15px;
    right: 15px;
    border-radius: 50%;
    color: #ffffff;
    text-align: center;
    font-size: 10px;
    border: 2px solid #9fd402;
    line-height: 21px;
    z-index: 1;
}

span.not-approve-listing {
    width: 25px;
    height: 25px;
    background: #dc3545;
    position: absolute;
    top: 15px;
    right: 15px;
    border-radius: 50%;
    color: #ffffff;
    text-align: center;
    font-size: 10px;
    border: 2px solid #dc3545;
    line-height: 21px;
}

a.author-img-box {
    position: absolute;
    right: 10px;
    max-width: 60px;
    top: -30px;
    background: rgba(255, 255, 255, 0.8);
    padding: 5px;
    border-radius: 50%;
    z-index: 999;
}

a.author-img-box img {
    border-radius: 50%;
}

.style-2 span.like-listing {
    left: 20px;
}

/*-------------- Liting Verticle Style -------------*/
.verticleilist.listing-shot {
    display: flex;
}

.verticleilist.listing-shot .listing-item {
    flex: 2;
    overflow: hidden;
    overflow: hidden;
    min-height: 220px;
}

.listing-shot-img {
    position: relative;
    min-height: 220px;
    max-height: 300px;
    height: 100%;
}

.verticleilist.listing-shot .listing-shot-img img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}

.verticleilist.listing-shot .verticle-listing-caption {
    flex: 5;
    bottom: 0;
    padding: 0;
    left: 0;
    position: relative;
    z-index: 50;
}

.verticleilist.listing-shot span.like-listing {
    position: absolute;
    right: 20px;
    top: 20px;
}

.verticleilist.listing-shot span.like-listing i {
    background: #ffffff;
    border: #a1acb5;
    color: #ff4e64;
    transition: all ease-in-out 0.4s;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
}

p.listing-description {
    max-width: 90%;
}

.verticleilist.listing-shot:hover span.like-listing i,
.verticleilist.listing-shot:focus span.like-listing i {
    background: #de6600;
    border: 1px solid transparent;
    color: #ffffff;
}

.listing-shot-img:before,
.listing-shot-img:after {
    content: "";
    position: absolute;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    -webkit-transition: all 0.6s ease-out 0s;
    -moz-transition: all 0.6s ease-out 0s;
    transition: all 0.6s ease-out 0s;
}

.listing-shot-img:hover:before {
    background-color: rgba(255, 255, 255, 0.2);
    right: 50%;
    left: 50%;
}

.listing-shot-img:hover:after {
    background-color: rgba(255, 255, 255, 0.2);
    top: 50%;
    bottom: 50%;
}

/*============================================================
listing Category Style
==============================================================*/
/*----------- listing Category Style 1 -----------*/
.category-boxs {
    height: 100%;

}

.category-box-full {
    position: relative;
    height: 100%;
    min-height: 350px;
    border-radius: 6px;
    overflow: hidden;
    margin-top: 25px;
}

.category-boxs {
    background: #f4f5f7;
    border-radius: 4px;
    display: block;
    height: 100%;
    margin-bottom: 15px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    overflow: hidden;
    cursor: pointer;
}

.category-boxs:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: 9;
    background: linear-gradient(to bottom, transparent 25%, #1e2a4c);
}

.category-box-content {
    position: absolute;
    bottom: 30px;
    left: 34px;
    width: calc(100% - 68px);
    z-index: 50;
    box-sizing: border-box;
}

.category-box-btn {
    position: absolute;
    right: 32px;
    bottom: 32px;
    z-index: 111;
    background-color: transparent;
    border: 1px solid #fff;
    color: #fff;
    padding: 8px 0px;
    text-align: center;
    min-width: 120px;
    border-radius: 50px;
    transition: all 0.3s;
}

.category-boxs:hover .category-box-btn,
.category-boxs:focus .category-box-btn {
    background: #de6600;
    border-color: #de6600;
    color: #ffffff;
}

.category-box-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    transition: transform 0.35s ease-out;
    transform: translate3d(0, 0, 0) scale(1);
    image-rendering: -webkit-optimize-contrast;
}

.category-box-content h3 {
    color: #fff;
    font-size: 24px;
    padding: 5px 0;
    margin: 0;
}

.category-box-content span {
    font-size: 16px;
    font-weight: 300;
    display: inline-block;
    color: rgba(255, 255, 255, 0.9);
}

.category-boxs:hover .category-box-bg {
    transform: translate3d(0, 0, 0) scale(1.08);
}

/*----------- listing Category Style 2 -----------*/
.category-box-full.style-2 {
    display: flex;
    align-items: center;
    text-align: center;
    background-size: cover;
    background-position: center;
}

.category-box-full.style-2:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    background: rgba(64, 65, 67, 0.2);
}

.category-box-full.style-2 .category-box-2 {
    width: 100%;
    position: relative;
}

.category-box-full.style-2 .category-box-2 i {
    width: 55px;
    height: 55px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid #ffffff;
    transition: all ease 0.4s;
    display: table;
    border-radius: 50%;
    margin: 10px auto;
    line-height: 52px;
    font-size: 20px;
    color: #ffffff;
}

.category-box-full.style-2:hover .category-box-2 i,
.category-box-full.style-2:focus .category-box-2 i {
    background: #de6600;
    border-color: #de6600;
    color: #ffffff;
}

.category-box-full.style-2 h3,
.category-box-full.style-2 a h3 {
    color: #ffffff;
    font-weight: 500;
    font-size: 22px;
}

/*----------- listing Category Style 3 -----------*/
.category-full-widget {
    border: none;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
	/*margin-top:25px;*/
	background:#ffffff;
	border:1px solid #eaeff5;
	box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -moz-box-shadow:0px 0px 10px 1px rgba(71, 85, 95,0.08)
}

.category-full-widget .category-widget-bg {
    padding: 20px;
    height: 150px;
    position: relative;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    background-size: cover;
    background-position: center;
    transition: transform 0.35s ease-out;
    transform: translate3d(0, 0, 0) scale(1);
    image-rendering: -webkit-optimize-contrast;

}

.cat-box-name {
    padding: 2em 0 2em 0;
    text-align: center;
}
.cat-box-name h4 {
    font-size: 12px;
}
.cat-box-name .btn-btn-wrowse {
    background: #ffffff;
    border-radius: 50px;
    padding: 9px 20px;
    min-width: 120px;
    display: inline-block;
    font-size: 16px;
    font-weight: 400;
    margin-top: 8px;
    margin-bottom: 20px;
    transition: all ease 0.4s;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
}

.cat-box-name .btn-btn-wrowse:hover,
.cat-box-name .btn-btn-wrowse:focus {
    background: #de6600;
    color: #ffffff;
}

.category-full-widget:hover .btn-btn-wrowse,
.category-full-widget:focus .btn-btn-wrowse {
    background: #de6600;
    color: #ffffff;
}

i.cat-icon {
    /*position: absolute;
    bottom: -25px;*/
    width: 52px;
    height: 52px;
    border-radius: 50px;
    line-height: 52px;
    font-size: 20px;
    text-align: center;
    color: #ffffff;
}

.category-full-widget:hover .category-widget-bg {
    transform: translate3d(0, 0, 0) scale(1.08);
}

/*==================================================
Popular Cities Style
====================================================*/
.place-box {
    height: 350px;
    display: inline-block;
    position: relative;
    width: 100%;
    overflow: hidden;
    z-index: 90;
    margin: 10px 0;
    border-radius: 6px;
}

.place-box-content {
    position: absolute;
    width: 100%;
    text-align: center;
    top: 50%;
    margin: 0 auto;
    z-index: 101;
    transform: translate(0, -50.5%);
}

.place-box-bg {
    background-size: cover;
    background-position: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: all 0.55s;
    position: absolute;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    transition: transform 0.35s ease-out;
}

.place-box-bg:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    background: linear-gradient(to bottom, transparent 25%, #1e2a4c);
}

.place-box:hover .place-box-bg {
    transform: scale(1.07);
}

.place-box-content h4 {
    font-size: 27px;
    line-height: 35px;
    color: #fff;
    margin: 0;
}

.place-box-content span {
    color: #fff;
}

.place-box-content a {
    font-size: 17px;
    color: #fff;
}

.listing-count {
    padding: 3px 25px;
    background: rgba(255, 255, 255, 0.2);
    position: absolute;
    top: 25px;
    left: 20px;
    z-index: 1;
    border-radius: 50px;
    color: #ffffff;
    font-size: 13px;
    border: 1px solid #ffffff;
}

/*====================================================
Testimonial Styles
====================================================*/
/*----------- Testimonial Styles 1 -------------------*/
.testimonial-1 {
    padding: 8em 0;
}

.testimonial-1 p {
    color: #ffffff;
}

.testimonial-1 .testimonial-detail {
    text-align: center;
    margin: 50px 10px 0;
    padding: 0 15px 10px 15px;
}

.testimonial-1 .testimonial-detail .pic {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 5px solid rgba(255, 255, 255, 0.3);
    display: inline-block;
    margin-top: -50px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.testimonial-1 .testimonial-detail .pic img {
    width: 100%;
    height: auto;
}

.testimonial-1 .testimonial-detail .user-description {
    font-size: 17px;
    line-height: 1.8;
    font-style: italic;
    font-family: initial;
    color: #ffffff;
    line-height: 30px;
    margin: 10px 0 20px;
}

.testimonial-1 .testimonial-detail .user-testimonial-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    color: #ffffff;
    text-transform: uppercase;
}

.testimonial-1 .testimonial-detail .user-post {
    display: block;
    font-size: 15px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 15px;
}

.testimonial-1 .testimonial-detail .user-post:before {
    content: "";
    width: 30px;
    display: block;
    margin: 10px auto;
    border: 1px solid #d3d3d3;
}

.testimonial-1 .owl-theme .owl-controls {
    margin-top: 30px;
}

.testimonial-1 .owl-theme .owl-controls .owl-pagination {
    width: 140px;
    padding: 10px;
    margin: 0 auto;
    line-height: 13px;
    background: #fe7f8b;
}

.testimonial-1 .owl-theme .owl-controls .owl-page span {
    width: 12px;
    height: 12px;
    border-radius: 0;
    background: transparent;
    border: 1px solid #fff;
}

.testimonial-1 .owl-theme .owl-controls .owl-page.active span,
.testimonial-1 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #fff;
}

/*----------- Testimonial Styles 2 -------------------*/

.testimonials-2 .testimonial-detail {
    margin: 0px 10px 0;
    padding: 30px 25px 20px;
    border-radius: 6px;
    background: #fff;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6
}

.client-detail-box {
    display: table;
    width: 100%;
    margin: 0 0 5px 0;
}

.testimonials-2 .testimonial-detail .pic {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, .3);
    display: inline-block;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .1);
    float: left;
}

.testimonials-2 .client-detail {
    display: table;
    margin-top: 15px;
    margin-left: 90px;
}

.testimonials-2 .testimonial-detail .pic img {
    width: 100%;
    height: auto
}

.testimonials-2 .testimonial-detail .description {
    font-size: 16px;
    line-height: 28px;
    margin: 10px 0 20px
}

.testimonials-2 .testimonial-detail .testimonial-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: #de6600;

}

.testimonials-2 .testimonial-detail .post {
    display: block;
    font-size: 16px;
    color: #8c9098;
    margin-bottom: 15px;
    /* text-transform: capitalize; */
    font-family: 'Lora', serif;
    font-style: italic;
}

/*----------- Testimonial Styles 3 -------------------*/
section.testimonials-3 {
    background-size: cover !important;
    background-position: center !important;
}

#testimonial-3 {
    text-align: center;
}

.client-detail-box {
    text-align: center;
}

.client-detail-box .pic img {
    max-width: 100px;
    border-radius: 50px;
    border: 6px solid #ebedf1;
    margin: 0 auto;
}

h3.testimonial-title {
    font-size: 23px;
}

span.post {
    font-size: 16px;
}

p.description {
    font-size: 18px;
    font-family: 'Lora', serif;
    font-style: italic;
}

/*=========================================================
Pricing Table Style
=========================================================*/
.pricing {
    background: #f4f5f7;
}

.package-box {
    background: #ffffff;
    border-radius: 6px;
    overflow: hidden;
    margin-top: 25px;
    text-align: center;
    padding: 0px;
    transition: all ease 0.4s;
    border: 1px solid #f4f5f7;
    -webkit-box-shadow: 0px 0px 10px 0px rgba(107, 121, 124, 0.2);
    box-shadow: 0px 0px 10px 0px rgba(107, 121, 124, 0.2);
}

.package-box:hover,
.package-box:focus {
    -webkit-box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
    -moz-box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
    box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
}

.package-header {
    padding: 25px 0 20px 0;
    background: #2a3646;
    border-radius: 0px;
}

.active .package-header {
    background: #de6600;
}

.package-header i {
    font-size: 3em;
    margin-bottom: 0.5rem;
    color: rgb(133, 148, 169);
}

.active .package-header i {
    color: rgba(255, 255, 255, 0.7);
}

.package-header h3 {
    /* text-transform: capitalize; */
    font-family: 'Muli', sans-serif;
    color: #7f90a7;
    font-size: 20px;
    font-weight: 500;
}

.active .package-header h3 {
    color: #ffffff;
}

.package-info ul {
    padding: 0 15px;
    margin: 0;
}

.package-info ul li {
    padding: 14px 0;
    font-size: 15px;
    list-style: none;
}

.package-info ul {
    padding: 0;
}

.package-info ul li:nth-of-type(2n+1) {
    background-color: #f6f8f9;
}

.package-price {
    padding: 20px 0;
}

.package-price h2 {
    color: #2a3646;
    font-weight: 600;
    font-size: 80px;
}

.active .package-price h2 {
    color: #28b15d;
}

.package-price h2 sup {
    font-size: 18px;
    font-weight: 500;
    opacity: 0.7;
    vertical-align: super;
}

.package-price h2 sub {
    font-size: 16px;
    font-weight: 500;
    opacity: 0.7;
}

button.btn.btn-package {
    background: #de6600;
    color: #ffffff;
    font-size: 18px;
    width: 100%;
    padding: 23px 45px;
    border-radius: 0px;
    margin-top: 10px;
    border: none;

}

button.btn.btn-package:hover,
button.btn.btn-package:focus {
    background: #2a3646;
}

/*========================================================
Services Style
=========================================================*/
.service {
    padding: 35px 0;
}

.service-box {
    text-align: center;
    padding: 30px 10px 30px;
    border: 1px solid transparent;
    transition: all 0.3s ease 0s;
    position: relative;
    z-index: 1;
    margin-bottom: 20px;
}

.service-box:after,
.service-box:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transition: all 0.5s ease 0s;
}

.service-box:after {
    border-bottom: 1px solid #de6600;
    border-top: 1px solid #de6600;
    transform: scaleX(0);
    transform-origin: 0 100% 0;
    z-index: -1;
}

.service-box:before {
    border-left: 1px solid #de6600;
    border-right: 1px solid #de6600;
    transform: scaleY(0);
    transform-origin: 100% 0 0;
    z-index: -1;
}

.service-box:hover:after,
.service-box.active:after {
    transform: scaleX(1)
}

.service-box:hover:before,
.service-box.active:before {
    transform: scaleY(1)
}

.service-box:hover .service-icon i,
.service-box:hover .service-content h3 a {
    color: #de6600;
}

.service-box .service-icon i {
    font-size: 32px;
    line-height: 32px;
    color: #636363;
    transition: all 0.3s ease 0s;
}

.service-box .service-content h3 {
    margin-bottom: 10px;
}

.service-box .service-content h3 a {
    font-size: 18px;
    line-height: 20px;
    color: #47555f;
    text-transform: uppercase;
    text-decoration: none;
}

.service-box .service-content h3 a:hover {
    color: #636363;
}

.service-box .service-content p {
    color: #828f99;
    line-height: 28px;
    font-size: 15px;
}

.service-box .read {
    margin-top: 20px;
}

.service-box .read a {
    border: 1px solid #636363;
    border-radius: 50%;
    color: #636363;
    display: inline-block;
    height: 18px;
    width: 18px;
    line-height: 16px;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease 0s;
    opacity: 0;
}

.service-box .read a:hover,
.service-box:hover .service-icon i {
    color: #de6600;
    border-color: #47555f;
}

.service-box.active .read a,
.service-box.active .service-icon i {
    color: #de6600;
    border-color: #47555f
}

.service-box:hover .read a,
.service-box.active .read a {
    opacity: 1;
    transition: all .3s ease 0s
}

/*========================================================
Features Style
=========================================================*/
.feature-box span {
    background: #de6600;
    width: 80px;
    height: 80px;
    display: table;
    border-radius: 50%;
    line-height: 80px;
    text-align: center;
    font-size: 28px;
    margin: 0 auto;
    color: #ffffff;
}

.feature-box {
    text-align: center;
    margin: 15px 0;
}

.feature-box h4 {
    font-size: 20px;
    margin: 15px 0 10px;
}

.feature-box p {
    font-size: 15px;
}

/*============================================
Counter Section Style
=============================================*/
.work-count {
    text-align: center;
}

.work-count span.icon {
    display: table;
    font-size: 20px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    background: #ffffff;
    border: 1px solid #ffffff;
    border-radius: 50%;
    margin: 0 auto 30px auto;
}

.work-count span.counter,
.work-count span.counter-incr {
    font-size: 50px;
    color: #ffffff;
}

.work-count p {
    font-size: 18px;
    color: #ffffff;
    margin-top: 5px;
}

/*=============================================
Tag Section Style
==============================================*/
section.tag-sec {
    background-size: cover !important;
    background-position: center !important;
    position: relative;
}

.tag-content {
    text-align: center;
    position: relative;
    padding: 50px 0;
}

.tag-content img {
    margin: 10px auto;
    display: table;
    max-width: 220px;
}

.tag-content h2 {
    text-transform: uppercase;
    font-weight: 600;
    color: #ffffff;
}

.tag-content p {
    color: #ffffff;
    font-size: 16px;
    line-height: 1.88;
}

body .tag-content a {
    color: #ffffff;
    display: inline-block;
    margin: 30px 0 0 0;
    transition: all ease 0.4s;
}

body .tag-content a:hover,
.tag-content a:focus {
    color: #de6600;
    background: #ffffff;
    border-color: #ffffff;
}

body .tag-content a:hover i,
.tag-content a:focus i {
    color: #de6600;
}

.tag-content a i {
    margin-left: 7px;
}

/*==============================================
Profile Page Styles
=============================================*/
.detail-section {
    position: relative;
    color: #fff;
    background-size: cover !important;
    background-position: center !important;
    height: 150px;
}

.detail-section .overlay {
    -webkit-backface-visibility: hidden;
    position: absolute;
    width: 100%;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    opacity: .4;
    background-color: #242429;
    transition: opacity .3s ease-in-out;
}

.detail-section-1 {
    position: relative;
    color: #fff;
    background-size: cover !important;
    background-position: center !important;
    height: 450px;
}

.detail-section-1 .overlay {
    -webkit-backface-visibility: hidden;
    position: absolute;
    width: 100%;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    opacity: .4;
    background-color: #242429;
    transition: opacity .3s ease-in-out;
}

.profile-cover-content {
    position: absolute;
    bottom: 20px;
    width: 100%;
    text-align: right;
    z-index: 10;
}

.cover-buttons>ul>li {
    position: relative;
    padding-bottom: 8px;
    list-style: none;
}

.buttons {
    padding: 15px 30px;
    display: inline-block;
    transition: all .2s ease;
    overflow: hidden;
    border: none;
    border-radius: 32px;
    line-height: 16px;
    transition: all .2s ease;
    width: 100%;
}

.cover-button {
    text-align: center;
}

.cover-buttons .button-outlined,
.cover-buttons .button-plain {
    padding: 14px 18px !important;
    font-weight: 400;
}

.cover-buttons .btn-outlined {
    color: #fff;
    border: 1px solid hsla(0, 0%, 100%, .5);
}

.cover-buttons .btn-outlined:hover,
.cover-buttons .btn-outlined:focus {
    color: #de6600;
    background: #ffffff;
    border: 1px solid #ffffff;
}

.cover-buttons .btn-outlined i,
.cover-buttons .button-plain i {
    margin-right: 8px;
    font-size: 17px;
}

.listing-rating {
    font-size: 16px;
    line-height: 1.2;
}

.listing-owner {
    position: absolute;
    min-width: 250px;
    bottom: -50px;
    left: 5%;
}

.listing-owner-avater {
    max-width: 80px;
    display: inline-block;
    float: left;
}

.listing-owner-detail {
    text-align: left;
    margin-left: 0px;
    padding: 16px 0 8px 90px;
    border-radius: 50px;
    height: 80px;
    background: #ffffff;
    margin-top: 0px;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

.listing-owner-detail h4 {
    margin-bottom: 1px;
    margin-top: 0;
}

/*===============================================
Page Title Settings
==============================================*/
.page-title {
    background-size: cover !important;
    background-position: center !important;
    position: relative;
    height: auto;
}

@media (max-width: 767px) {
    .page-title {
        height: 250px;
    }
}

.page-title:before {
    content: "";
    display: block;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    position: absolute;
    background: #007a7a;
    opacity: 0.8;
}

.title-content {
    position: relative;
    text-align: center;
    color: #ffffff;
    margin-top: 70px;
    z-index: 1;
}

@media(max-width: 767px) {
    .title-content {
        position: relative;
        text-align: center;
        color: #ffffff;
        margin-top: 70px;
    }
}

.title-content h1 {
    color: #ffffff;
    margin-bottom: 3rem;
}

.title-content a {
    color: #de6600;
    text-transform: none;
    text-decoration: underline;
}

span.gt3_breadcrumb_divider {
    width: 25px;
    height: 25px;
    display: inline-block;
    position: relative;
    font-family: FontAwesome;
}

span.gt3_breadcrumb_divider:before {
    content: "\f105";
    font-size: 15px;
    font-weight: 600;
}

/*==============================================
Component Style
===============================================*/
/*------------ Component: Accordion 1 -------*/
#accordion .panel {
    border: none;
    box-shadow: none;
    border-radius: 10px;
    margin-bottom: 10px;
}

#accordion .panel-heading {
    padding: 0;
    border: none;
    border-radius: 10px;
}

#accordion .panel-title a {
    display: block;
    padding: 15px 35px;
    font-size: 20px;
    font-weight: 600;
    color: #007a7a;
    background: #ffffff;
    border: none;
    position: relative;
    transition: all 0.3s ease 0s;
    border: 1px solid #eaeff5;
}

#accordion .panel-title a:after,
#accordion .panel-title a.collapsed:after {
    content: "\f068";
    font-family: fontawesome;
    width: 32px;
    height: 32px;
    line-height: 25px;
    border-radius: 50%;
    background: #ffffff;
    text-align: center;
    font-size: 14px;
    color: #de6600;
    border: 4px solid #eaeff5;
    position: absolute;
    top: 10px;
    left: -20px;
    transition: all 0.3s ease 0s;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

#accordion .panel-title a.collapsed:after {
    content: "\f067";
}

#accordion .panel-title a:hover:after,
#accordion .panel-title a.collapsed:hover:after {
    transform: rotate(360deg);
}

#accordion .panel-body {
    padding: 15px 25px;
    background: #fff;
    font-size: 16px;
    color: #8c8c8c;
    line-height: 25px;
    border-top: none;
    position: relative;
}

/*------------ Component: Accordion 2 -------*/
#accordion2 .panel {
    border: none;
    box-shadow: none;
    border-radius: 10px;
    margin-bottom: 10px;
}

#accordion2 .panel-heading {
    padding: 0;
    border: none;
    border-radius: 10px;
}

#accordion2 .panel-title a {
    display: block;
    padding: 15px 35px;
    font-size: 20px;
    font-weight: 600;
    color: #007a7a;
    background: #ffffff;
    border: none;
    position: relative;
    transition: all 0.3s ease 0s;
    border: 1px solid #eaeff5;
}

#accordion2 .panel-title a:after,
#accordion2 .panel-title a.collapsed:after {
    content: "\f068";
    font-family: fontawesome;
    width: 32px;
    height: 32px;
    line-height: 25px;
    border-radius: 50%;
    background: #ffffff;
    text-align: center;
    font-size: 14px;
    color: #de6600;
    border: 4px solid #eaeff5;
    position: absolute;
    top: 10px;
    left: -20px;
    transition: all 0.3s ease 0s;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

#accordion2 .panel-title a.collapsed:after {
    content: "\f067";
}

#accordion2 .panel-title a:hover:after,
#accordion2 .panel-title a.collapsed:hover:after {
    transform: rotate(360deg);
}

#accordion2 .panel-body {
    padding: 15px 25px;
    background: #fff;
    font-size: 16px;
    color: #8c8c8c;
    line-height: 25px;
    border-top: none;
    position: relative;
}

#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background: #de6600;
}

@media (max-width: 1600px) {
    .category-box-content {
        bottom: 95px;
    }

    .category-box-btn {
        left: 32px;
        right: auto;
    }
}

/*------------ Component: Tab -------*/
.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: none;
    border-bottom-color: transparent;
}

.tab-content>.tab-pane {
    padding: 0;
}

/*------------ Component: Tab Style 1 -------*/
.tab.style-1 {
    background: #fbfbfb;
    border-radius: 32px;
    overflow: hidden;
    /*border: 1px solid #eaeff5;
	box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);*/
}

.nav-div h5 a {
    padding: 10px;
}

.nav-tabs .nav-link {
    font-size: 20px;
    padding: 0.5rem !important;
    margin-left: 0;
    text-transform: none;
    color: #26354e;
    opacity: 0.7;
    margin-right: 3rem;
    margin-bottom: 3rem;
    font-weight: 600;
}

.tab .nav-tabs {
    position: relative;
    border: none;
}

.tab .nav-tabs li {
    text-align: center;
    margin-right: 0;
    font-family: 'Ysabeau', serif;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    color: #007a7a;
    background-color: transparent;
    border-color: transparent;
    opacity: 1;
    border-bottom: 6px solid #007a7a;
    border-radius: 3px;
    font-weight: 800;

}

.tab .nav-tabs li a {
    font-size: 18px;
    font-weight: 100;
    text-transform: uppercase;
    padding: 15px 30px;
    background: #fff;
    margin-right: 0;
    border-radius: 0;
    border: none;
    border-right: 1px solid #eaeff5;
    position: relative;
    transition: all 0.5s ease 0s;
}

.tab .nav-tabs li:last-child a,
.tab .nav-tabs li:last-child.active a,
.tab .nav-tabs li:last-child a:hover {
    border-right: 1px solid #eaeff5;
}

.tab .nav-tabs li a:hover,
.tab .nav-tabs li.active a {
    color: #de6600;
    border-bottom: 2px solid #de6600;
}

.tab .tab-content {
    font-size: 16px;
    padding: 20px 0;
    margin-top: 0 !important;
}

.tab .tab-content h3 {
    font-size: 16px;
    margin-top: 2rem;
    font-weight: normal;
    opacity: 0.6;
}

/* Add this at the end of the file to prevent horizontal scrolling */
html, body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
}

/* Fix for potential issues with containers extending beyond viewport */
.container, .container-fluid, .row {
    max-width: 100%;
}

/* Ensure images don't cause overflow */
img {
    max-width: 100%;
    height: auto;
}

/* Fix for mobile horizontal scrolling in sliders */
.slick-slider {
    width: 100%;
    overflow: hidden;
}

/* Fix for tabs on mobile */
@media (max-width: 767px) {
    .tab .nav-tabs, .nav-tabs {
        width: 100%;
        padding-right: 0;
    }
}

@media only screen and (max-width: 480px) {
    .tab .nav-tabs li {
        width: 100%;
        border-right: 1px solid #ddd;
        margin-bottom: 8px;
    }
}

/*------------ Component: Tab Style 2 -------*/
.tab.style-2 {
    background: #ffffff;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

.tab.style-2 .nav-tabs li a:hover,
.tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #de6600;
    background: #de6600;
}

/*------------ Component: Notify Style -------*/
.notice {
    padding: 15px;
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 2px;
    border: 1px solid #eaeff5;
    border-left: 6px solid #838992;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08)
}

.notice-sm {
    padding: 10px;
    font-size: 80%;
}

.notice-lg {
    padding: 35px;
    font-size: large;
}

.notice-success {
    border-left-color: #74ba28;
}

.notice-success>strong {
    color: #74ba28;
}

.notice-info {
    border-left-color: #1db4bd;
}

.notice-info>strong {
    color: #1db4bd;
}

.notice-warning {
    border-left-color: #fea911;
}

.notice-warning>strong {
    color: #fea911;
}

.notice-danger {
    border-left-color: #eb344f;
}

/*------------ Component: Alert Style -------*/
.alert-success {
    color: #74ba28;
    background-color: #e6ffcb;
    border-color: #d4f9ac;
}

.alert-info {
    color: #4dccd3;
    background-color: #d0fdff;
    border-color: #b2fbff;
}

.alert-warning {
    color: #ffbc44;
    background-color: #fff6e5;
    border-color: #ffe2ae;
}

.alert-danger {
    color: #ff526c;
    background-color: #ffe5e9;
    border-color: #ffa7b4;
}

.close {
    opacity: 0.8;
}

/*==========================================
Newsletter
==========================================*/
section.newsletter.bg-newsletter {
    background-size: cover !important;
    background-position: center !important;
}

.newsletter p {
    font-size: 15px;
}

.newsletter.inverse-theme h2,
.newsletter.inverse-theme p {
    color: #ffffff;
}

.sup-form {
    margin-top: 30px;
    display: block;
    position: relative;
}

.sup-form .form-control {
    width: 100%;
    font-size: 16px;
    font-weight: 400;
    background-color: #ffffff;
    border: 1px solid #ffffff;
    padding: 20px 20px 18px;
    border-radius: 2px;
    height: 70px;

}

footer .sup-form .form-control {
    height: 60px;

}

.sup-form .form-control:hover,
.sup-form .form-control:focus {
    border: 1px solid #ffffff;
}

.sup-form .btn {
    bottom: 5px;
    position: absolute;
    right: 4px;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 1px;
    padding: 14px 25px 14px;
    text-align: center;
    text-decoration: none;

    white-space: normal;
    width: auto;
    border-width: 0;
    cursor: pointer;
    height: 60px;
    border-radius: 2px;
}

footer .sup-form .btn {
    height: 50px;
}

.sup-form .btn:hover,
.sup-form .btn:focus {
    border: none;
}

.news-white-btn {
    background: #ffffff;
    border: 1px solid #ffffff;
}

.widget .widget-caption .col-xs-8 {
    height: 100%;
}

.widget .widget-detail h3 {
    font-size: 16px !important;
    font-weight: normal;
    margin-bottom: 0 !important;
    line-height: 2.9 !important;
    margin-top: 0 !important;
}

/*==========================================
Footer Styles
==========================================*/
.footer-widget {
    padding: 4em 0;
    text-align: left;
    color: #ffffff;
    line-height: 1.8;
    display: inline-block;
    width: 100%;
    font-size: 16px;
}

.light-footer .footer-widget {
    color: #6b7684;
}

.dark-footer .footer-widget {
    color: #899bb3;
}

.footer-widget img {
    margin-bottom: 10px;
    max-width: 225px;
}

.footer .theme-bg p {
    color: #ffffff;
}

.footer-widget p {
    line-height: 1.7;
    color: #899bb3;
}

.footer-widget p a {
    color: #ffffff;
}

.widget-title {
    color: #fff;
    font-weight: 500;
    /* text-transform: capitalize; */
    font-size: 20px;
    margin-bottom: 20px
}

.footer-social {
    text-align: left;
    padding: 0;
    margin: 0;
}

.footer-social li {
    list-style: none;
    display: inline-block;
}

.footer-social li a {
    color: #899bb3;
    font-size: 15px;
    letter-spacing: 0.5px;
}

.footer-social li a i {
    font-size: 20px;
    border-radius: 6%;
    text-align: center;
    background: transparent;
    transition: all ease 0.4s;
    color: #7f90a7;
    margin-right: 15px;
}

.footer-social li a:hover i {
    background: transparent !important;
    color: #ffffff;
}

.footer-copyright {
    background: #007a7a;
    width: 100%;
    display: inline-block;
    text-align: center;
    padding: 15px 20px 8px 20px;
}

ul.footer-navigation {
    margin: 0;
    padding: 0;
}

ul.footer-navigation.sinlge li {
    list-style: none;
    width: 100%;
    display: inline-block
}

ul.footer-navigation li {
    list-style: none;
    width: 50%;
    float: left;
    display: inline-block;
}

ul.footer-navigation li a {
    margin-bottom: 10px;
    display: inline-block;
    color: #7f90a7;
}

.footer-copyright p {
    font-size: 15px;
    color: #7f90a7;
    font-weight: 400;
}

.footer-copyright p a {
    color: #de6600;
}

.sing-add {
    margin-bottom: 15px;
    width: 100%;
    padding: 0px 0px 0px 30px;
    position: relative;
}

.sing-add i {
    position: absolute;
    left: 0;
    top: 4px;
    font-size: 19px;
}

.dark-footer .sup-form .form-control,
.dark-footer .sup-form .form-control:hover,
.dark-footer .sup-form .form-control:focus {
    background-color: transparent !important;
    border: 2px solid #394863;
    padding: 15px 12px 18px;
    color: #7b8a9e;
}

.dark-footer .sup-form .btn {
    padding: 14px 13px 14px;
    color: #7b8a9e;
    background: transparent !important;
}

.dark-footer .form-control::-moz-placeholder {
    color: #7b8a9e;
}

.dark-footer .form-control:-ms-input-placeholder {
    color: #7b8a9e;
}

.dark-footer .form-control::-webkit-input-placeholder {
    color: #7b8a9e;
}

.other-store-link {
    width: auto;
    margin-top: 12px;
    display: inline-block;
}

.other-store-app {
    width: 200px;
    height: 62px;
    border: 2px solid #ffffff;
    border-radius: 4px;
    padding: 0 14px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: #ffffff;
}

.light-footer .other-store-app {
    border: 2px solid #7f90a7;
    color: #7f90a7;
}

.dark-footer .other-store-app {
    border: 2px solid #394863;
    color: #7f90a7;
}

.os-app-icon {
    margin-right: 13px;
}

.os-app-icon i {
    font-size: 32px;
}

.os-app-caps {
    font-size: 18px;
    font-weight: 600;
}

/*------- Light Footer -----------*/
.footer.light-footer {
    background: #ffffff;
    border-top: 1px solid #f1f3fb;
}

.footer.light-footer .widget-title {
    color: #2a3646;
}

.footer.light-footer .footer-widget p a {
    color: #7f90a7;
}

.footer.light-footer .footer-social li a i {
    background: transparent !important;
    color: #7f90a7;
}

.footer.light-footer .footer-copyright {
    background: #ffffff;
    border-top: 1px solid #f1f3fb;
}

.footer.light-footer .sup-form .form-control {
    border: 1px solid #ccd3de;
}

/*----------- Image background Footer ----------*/
footer.footer.image-bg {
    position: relative;
    background-size: cover !important;
    background-position: center !important;
}

footer.footer.image-bg:before {
    content: "";
    position: absolute;
    background: #4b6282;
    display: block;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    opacity: 0.7;
}

.footer.image-bg .footer-copyright {
    background: transparent;
    border-top: 1px solid #f1f3fb;
}

.footer.image-bg ul.footer-navigation li a,
.footer.image-bg .footer-widget p,
.footer.image-bg .footer-copyright p {
    color: #ffffff
}

.footer.image-bg .footer-social li a i {
    background: transparent !important;
    color: #ffffff;
}

.footer.image-bg .theme-btn i {
    font-size: 20px;
}

/*=======================================
Dropper Settings
========================================*/
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
    background-color: #ffffff;
    opacity: 1;
}

div.datedropper.my-style {
    border-radius: 8px;
    width: 180px;
}

div.datedropper.my-style .picker {
    border-radius: 8px;
    box-shadow: 0 0 32px 0px rgba(0, 0, 0, 0.1);
}

div.datedropper.my-style .pick-l {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

div.datedropper.my-style:before,
div.datedropper.my-style .pick-submit,
div.datedropper.my-style .pick-lg-b .pick-sl:before,
div.datedropper.my-style .pick-m,
div.datedropper.my-style .pick-lg-h {
    background-color: #1cc100;
}

div.datedropper.my-style .pick-y.pick-jump,
div.datedropper.my-style .pick li span,
div.datedropper.my-style .pick-lg-b .pick-wke,
div.datedropper.my-style .pick-btn {
    color: #1cc100;
}

div.datedropper.my-style .picker,
div.datedropper.my-style .pick-l {
    background-color: #FFF;
}

div.datedropper.my-style .picker,
div.datedropper.my-style .pick-arw,
div.datedropper.my-style .pick-l {
    color: #3a465e;
}

div.datedropper.my-style .pick-m,
div.datedropper.my-style .pick-m .pick-arw,
div.datedropper.my-style .pick-lg-h,
div.datedropper.my-style .pick-lg-b .pick-sl,
div.datedropper.my-style .pick-submit {
    color: #FFF;
}

div.datedropper.my-style.picker-tiny:before,
div.datedropper.my-style.picker-tiny .pick-m {
    background-color: #FFF;
}

div.datedropper.my-style.picker-tiny .pick-m,
div.datedropper.my-style.picker-tiny .pick-m .pick-arw {
    color: #3a465e;
}

div.datedropper.my-style.picker-lkd .pick-submit {
    background-color: #FFF;
    color: #3a465e;
}

/*=======================================
Slick Slider
=========================================*/
.list-slide-box {
    padding: 0 10px;
}

.slick-prev,
.slick-next {
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: 50%;
    display: block;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    padding: 0;
    cursor: pointer;
    color: transparent;
    border: none;
    outline: none;
    background: transparent;
}

.slick-prev:before,
.slick-next:before {
    font-family: 'FontAwesome';
    font-size: 12px;
    line-height: 1;
    opacity: 1;
    color: #de6600;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    line-height: 26px;
    text-align: center;
    display: inline-block;
    background: #ffffff;
    border: 1px solid #eaeff5;
    transition: all ease 0.4s;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.slick-initialized .slick-slide {
    outline: none;
}

.slick-prev:hover:before,
.slick-prev:focus:before,
.slick-next:hover:before,
.slick-next:focus:before {
    background: #de6600;
    color: #ffffff;
}

.slick-next {
    right: -25px;
}

.slick-prev {
    left: -35px;
}

.slick-prev:before {
    content: '\f060';
}

.slick-next:before {
    content: '\f061';
}

/*==================================================
Pages Settings
=================================================*/
.detail-wrapper {
    width: 100%;
    display: block;
    overflow: hidden;
    border-radius: 32px;
    margin-bottom: 40px;
    background: #fbfbfb;
    /* box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);*/
}

-box {
    background: #ffffff;
    border-radius: 6px;
}

-shadow {
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

.detail-wrapper-body {
    padding: 1.5em 1em 1.5em 1.5em;
}

.detail-wrapper-header {
    padding: 1em 1em 1em 1.5em;
    border-bottom: 1px solid #eaeff5;
}

.detail-wrapper-header h4 {
    margin: 0;
}

.detail-list-rating {
    float: left;
    margin-right: 15px;
}

.detail-list-rating i {
    color: #ffc107;
}

span.category-tag {
    font-size: 12px !important;
    color: #de6600;
    border: 1px solid #de6600;
    padding: 0px 10px;
    border-radius: 50px;
    position: relative;
}

.detail-wrapper-body ul.detail-check {
    margin: 0;
    padding: 0;
    width: 100%;
    display: inline-table;
}

.detail-wrapper-body ul.detail-check li {
    list-style: none;
    display: block;
    padding: 6px 0 5px 30px;
    position: relative;
}

#equipement .side-list>ul>li:first-child {
    font-size: 14px;
    font-weight: normal;
    opacity: 0.6;
}

.annonces {
    margin-top: 0px;
}

.annonces a {
    margin-right: 3rem;
}

.annonces a i {
    margin-right: 3px;
}

.detail-wrapper-body ul.detail-check li:before {
    width: 19px;
    height: 19px;
    position: relative;
    content: "\f00c";
    font-family: "FontAwesome";
    font-size: 12px;
    color: #ffffff;
    display: inline-block;
    left: 0;
    top: 8px;
    text-align: center;
    line-height: 16px;
    background-color: #de6600;
    border: 2px solid transparent;
    transition: border-color 0s;
    border-radius: 4px;
    position: absolute;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

button.btn.remove-field.light-gray-btn {
    margin-left: 15px;
}

/*---------- Add listing ---------*/
.add-listing-box {
    background: #ffffff;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
}

.listing-box-header {
    text-align: center;
    margin: 25px 0 35px 0;
}

.listing-box-header i {
    font-size: 2.5em;
}

.listing-box-header p {
    font-size: 16px;
}

.opening-day label.control-label {
    margin-top: 10px;
    margin-bottom: 10px;
}


/*----------- Edit Wraper -------------*/
.edit-info .avater-box {
    width: 140px;
    margin: 0 auto;
    height: 140px;
    position: relative;
    display: inline-block;
    background: #ffffff;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
    border-radius: 50%;
    padding: 10px;
}

.upload-btn-wrapper {
    position: absolute;
    overflow: hidden;
    display: inline-block;
    bottom: -3px;
    left: 0;
}

.edit-info .btn {
    border: 1px solid #de6600;
    color: #de6600;
    background-color: #ffffff;
    padding: 6px 16px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: bold;
}

.upload-btn-wrapper input[type=file] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
}


/*------------------ preview Listing -------------------*/
.preview-info label {
    font-weight: 400;
    margin-bottom: 20px;
}

i.preview-icon {
    width: 35px;
    height: 35px;
    display: inline-block;
    border: 1px solid transparent;
    line-height: 35px;
    text-align: center;
    font-size: 15px;
    border-radius: 50px;
}

i.preview-icon.web {
    background: rgba(58, 204, 212, 0.1);
    color: #3accd4;
    border-color: #3accd4;
}

i.preview-icon.email {
    background: rgba(241, 61, 88, 0.1);
    color: #f13d58;
    border-color: #f13d58;
}

i.preview-icon.call {
    background: rgba(74, 173, 23, 0.1);
    color: #4aad17;
    border-color: #4aad17;
}

i.preview-icon.birth {
    background: rgba(254, 170, 17, 0.1);
    color: #fea911;
    border-color: #fea911;
}

.preview-info-header {
    border-bottom: 1px solid #e9ecef;
    padding-left: 15px;
}

.preview-info-body {
    padding: 20px 15px 0 15px;
}

ul.info-list {
    padding: 0px;
    margin: 0;
}

ul.info-list li {
    list-style: none;
    display: block;
    padding: 5px 0 10px 0;
}

ul.info-list li label {
    min-width: 110px;
}

ul.social-info.info-list li i {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: #ffffff;
    line-height: 38px;
    text-align: center;
    color: #de6600;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
}

.nav-pills>li.active>a,
.nav-pills>li.active>a:focus,
.nav-pills>li.active>a:hover {
    color: #fff;
    background-color: #2c81e8;
}

label.prizm {
    display: block;
}

/*------------- Dropzone Settings ----------------*/
.dropzone {
    border: 1px dashed #eaeff5;
    max-width: 80%;
    margin: 14.4px auto;
    padding: 25px 0;
    min-height: 50px;
    color: #677897;
    display: block;
    background: transparent;
}

.dropzone i {
    display: block;
    font-size: 35px;
    margin-bottom: 10px;
}

.dropzone .dz-message {
    text-align: center;
    margin: 0em 0;
}

/*-------------- Review Box ------------*/
.review-body {
    padding-left: 90px;
    position: relative;
}

.detail-wrapper-body .review-list {
    margin: 0;
    padding: 0;
}

.detail-wrapper-body .review-list li {
    list-style: none;
    padding: 2em 1em;
    border-bottom: 1px solid #eaeff5;
}

.detail-wrapper-body .review-list li:last-child {
    border-bottom: none;
}

.review-avatar {
    width: 70px;
    height: 70px;
    top: -12px;
    vertical-align: top;
    position: absolute;
    left: 2px;
    padding: 5px;
    border-radius: 50%;
    display: inline-block;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

.review-avatar img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 50%;
}

.review-body .review-info {
    overflow: hidden;
}

.review-comment {
    float: left;
    width: 50%;
}

.review-content p {
    font-size: 15px;
    line-height: 1.8;
}

.review-body .review-author {
    font-size: 16px;
    line-height: 24px;
    color: #007a7a;
    font-weight: 500;
}

.review-comment-date {
    float: right;
    width: 50%;
    text-align: right;
}

.review-date span {
    font-size: 0.97em;
    line-height: 24px;
}

.review-comment-stars i {
    color: #FFC107;
}

.review-comment-stars i.empty {
    color: #b9c0c5;
}

/*------------ payment Options ------------*/
.payment-card {
    border-radius: 4px;
    padding: 18px 15px 15px 15px;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    margin-bottom: 20px;
}

.payment-card-title h4 {
    margin: 0;
}

.payment-card-title.flexbox {
    float: left;
}

header.payment-card-header {
    display: inline-block;
    width: 100%;
}

header.payment-card-header:hover,
header.payment-card-header:focus {
    cursor: pointer;
}

header.payment-card-header .pull-right img {
    max-width: 100px;
}

/*----------------- Invoice Page ------------------*/
.table-bordered>tbody>tr>td,
.table-bordered>tbody>tr>th,
.table-bordered>tfoot>tr>td,
.table-bordered>tfoot>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>thead>tr>th {
    border: 1px solid #eaeff5;
}

.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
    padding: 15px 12px;
    line-height: 1.42857143;
    vertical-align: top;
    border-top: 1px solid #eaeff5;
}

.table-bordered {
    border: 1px solid #eaeff5;
}

.table-striped>tbody>tr:nth-of-type(odd) {
    background-color: #f5f7f9;
}

p#invoice-info {
    text-align: right;
}

.detail-invoice p br {
    line-height: 2.2;
}

.detail-invoice p {
    font-size: 15px;
}

/*---------------- Booking Confirmation --------------------*/
.booking-confirm {
    text-align: center;
}

.booking-confirm p {
    font-size: 17px;
    text-transform: initial;
}

.booking-confirm i {
    width: 100px;
    height: 100px;
    display: table;
    margin: 0 auto;
    background: #ffffff;
    line-height: 100px;
    font-size: 45px;
    color: #05bf83;
    border-radius: 50%;
    border: 1px solid #abffe4;
    box-shadow: 0px 0px 10px 1px rgb(175, 255, 229);
    -webkit-box-shadow: 0px 0px 10px 1px rgb(175, 255, 229);
    -moz-box-shadow: 0px 0px 10px 1px rgb(175, 255, 229);
}

.booking-confirm h2 {
    color: #05bf83;
}

.funky-font {
    font-family: 'Crimson Text', serif;
    font-style: italic;
}

/*----------------- Error Page Style --------------------*/
.error-page {
    text-align: center;
}

.error-page p {
    font-size: 17px;
    text-transform: initial;
}

.error-page i {
    width: 100px;
    height: 100px;
    display: table;
    margin: 0 auto;
    background: #ffffff;
    line-height: 100px;
    font-size: 45px;
    color: #f21136;
    border-radius: 50%;
    border: 1px solid #ffcad3;
    box-shadow: 0px 0px 10px 1px rgb(255, 180, 193);
    -webkit-box-shadow: 0px 0px 10px 1px rgb(255, 180, 193);
    -moz-box-shadow: 0px 0px 10px 1px rgb(255, 180, 193);
}

/*----------- Filter Option ----------*/
.layout-option a {
    font-size: 17px;
    margin-left: 10px;
    opacity: 0.7;
    padding: 8px 10px;
    border: 1px solid #eaeff5;
    border-radius: 2px;
}

.layout-option a:hover,
.layout-option a:focus {
    opacity: 1;
    color: #de6600;
    ;
}

.layout-option a.active {
    opacity: 1;
    color: #de6600;
}

/*------------- TranslateY ----------------*/
.translateY-20 {
    -webkit-transform: translateY(-20px);
    -o-transform: translateY(-20px);
    transform: translateY(-20px);
}

.translateY-40 {
    -webkit-transform: translateY(-40px);
    -o-transform: translateY(-40px);
    transform: translateY(-40px);
}

.translateY-60 {
    -webkit-transform: translateY(-60px);
    -o-transform: translateY(-60px);
    transform: translateY(-60px);
}

.translateY-80 {
    -webkit-transform: translateY(-80px);
    -o-transform: translateY(-80px);
    transform: translateY(-80px);
}

.translateY-100 {
    -webkit-transform: translateY(-100px);
    -o-transform: translateY(-100px);
    transform: translateY(-100px);
}

/*--------------- Manage Listing ----------------*/
.small-list-wrapper ul {
    padding: 0;
    margin: 0;
}

.small-list-wrapper ul li {
    list-style: none;
    margin-bottom: 10px;
    width: 100%;
    display: block;
}

.small-listing-box {
    display: table;
    border-radius: 2px;
    position: relative;
    align-items: center;
    transition: all ease 0.4s;
    width: 100%;
}

.small-listing-box:hover,
.small-listing-box:focus {
    cursor: pointer;
}

.small-list-action {
    float: right;
    margin-right: 15px;
    padding-top: 15px;
}

.small-list-detail {
    margin-top: 8px;
}

.small-list-img {
    max-width: 80px;
    padding: 5px;
    float: left;
    margin-right: 15px;
}

.small-list-img img {
    border-radius: 2px;
}

.small-list-detail {
    display: inline-block;
}

.small-list-detail h4 {
    margin-bottom: 2px;
}

.manage-listing .form-group {
    display: flex;
    align-items: center;
}

.avater-status {
    width: 12px;
    height: 12px;
    position: absolute;
    background: #07b107;
    line-height: 12px;
    border-radius: 50%;
    right: 26px;
    bottom: 8px;
}

.status-pulse {
    box-shadow: 0 0 0 rgba(7, 177, 7, .55);
    animation: status-pulse 2s infinite;
}

@-webkit-keyframes status-pulse {
    0% {
        -webkit-box-shadow: 0 0 0 0 rgba(7, 177, 7, .55)
    }

    70% {
        -webkit-box-shadow: 0 0 0 10px rgba(7, 177, 7, 0)
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(7, 177, 7, 0)
    }
}

@keyframes status-pulse {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(7, 177, 7, .55);
        box-shadow: 0 0 0 0 rgba(7, 177, 7, .55)
    }

    70% {
        -moz-box-shadow: 0 0 0 10px rgba(7, 177, 7, 0);
        box-shadow: 0 0 0 10px rgba(7, 177, 7, 0)
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(7, 177, 7, 0);
        box-shadow: 0 0 0 0 rgba(7, 177, 7, 0)
    }
}

/*==========================================
Sidebar Style
==========================================*/
.widget-boxed {
    background-color: #fbfbfb;
    border-radius: 16px;
    transform: translate3d(0, 0, 0);
    z-index: 90;
    margin: 10px 0 10px 0px;
    position: relative;
    padding: 1rem;
    /*	padding:0 12px 32px 20px;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);*/
}

.widget-boxed-header {
    /*padding: 14px 0;
    border-bottom: 1px solid #eaeff5;*/
}

.widget-boxed-header h4 {
    margin-top: 20px;
    font-weight: 700;
}

.widget-boxed-header h4>i {
    position: relative;
    top: 3px;
    margin-right: 10px;
}

/* .widget-boxed-body {
    padding: 0 20px;
} */

.input-group .form-control,
.input-group-addon,
.input-group-btn {
    display: table-cell;
}

.input-number {
    background: transparent;
}

.input-number:hover,
.input-number:focus {
    background: transparent;
    border: none;
}

.btn.counter-btn {
    border-radius: 50% !important;
    height: 35px;
    width: 35px;
    padding: 0;
    margin-left: 0px !important;
    background: #ffffff;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

.btn.counter-btn:hover,
.btn.counter-btn:focus {
    background: #de6600;
    color: #ffffff;
}

.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group {
    margin-right: 0;
}

/*------------- Opening hour ----------------*/
.side-list ul {
    margin: 0;
    padding: 0;
}

.side-list ul li {
    list-style: none;
    padding: 10px 5px 10px 5px;
    display: inline-block;
    width: 100%;
    border-bottom: 1px dashed #eaeff5;
}

.side-list.no-border ul li {
    border-bottom: none;
}

.side-list ul li:last-child {
    border-bottom: none;
}

.side-list ul li span {
    float: right;
    font-weight: 500;
}

.side-list-inline ul {
    margin: 0;
    padding: 0;
}

.side-list-inline li {
    list-style: none;
    display: inline-block;
    margin-right: 10px;
    padding: 10px 5px 10px 5px;
}

ul.side-list-inline.social-side li {
    display: inline-block;
    width: auto;
}

ul.side-list-inline.social-side li {
    padding: 10px 0px 10px 3px;
}

ul.side-list-inline.social-side li a i {
    width: 45px;
    height: 45px;
    display: inline-block;
    line-height: 45px;
    text-align: center;
    border-radius: 50%;
    transition: all ease 0.4s;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

ul.side-list-inline.social-side li a :hover,
ul.side-list-inline.social-side li a :focus {
    background: #de6600;
    color: #ffffff;
}

/*----------------- Listing In Sidebar -------*/
.listing-list-img {
    display: inline-block;
    width: 55px;
    max-width: 55px;
    overflow: hidden;
    height: 55px;
    border-radius: 4px;
    position: relative;
    margin: 0 10px 0 0;
    vertical-align: text-bottom;
}

.side-list ul.listing-list li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 10px 5px 10px 5px;
    align-items: center;
}

.side-list .listing-post-meta span {
    float: none;
}

.listing-post-meta {
    font-size: 90%;
}

.listing-list-info h5 {
    margin: 0;
}

.leaflet-control span {
    float: none !important;
}

.help-support i {
    display: inline-block;
}

.help-support p {
    margin-bottom: 5px;
}

ul.price-range li span {
    float: left;
    margin-bottom: 0;
}

.offcanvas.show:not(.hiding),
.offcanvas.showing {
    transform: none;
    overflow: auto;
    padding: 15px;
}

.page-name>.container {
    padding: 5rem 2rem;
}

.social-network {
    /* color: #fff; */
    display: inline-block;
    font-size: 16px;
    /* font-weight: 500; */
    padding: 6px 15px;
    border-radius: 4px;
    margin-right: 3px;
    border: 1px solid transparent;
}

.li-btn {
    width: 100%;
    background: #ffffff;
    color: inherit;
    -webkit-user-select: none;
    border-radius: 32px;
    border-color: #007a7a;
    ;
}


.li-btn.view:hover {
    color: gray;
}

.li-btn:hover {}

.share-button:hover {}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.9);
    }

    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 1s infinite;
}

@media (max-width: 768px) {
    .nav-div {
        text-align: center !important;
    }
}


.widget {
    border: 1px solid #f4f5f7;
    background: #ffffff;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 12px 0;
    box-shadow: 0px 0px 9px 0px rgba(64, 65, 67, 0.05);
}

.widget.unique-widget i.icon {
    font-size: 20px;
    line-height: 45px;
    text-align: center;
    display: table;
    margin: 0 auto;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: #ffffff;
}

.widget.unique-widget .widget-caption.info i.icon {
    background: #ffffff;
    border: 1px solid #2196f3;
    color: #2196f3;
}

.widget.unique-widget .widget-caption.danger i.icon {
    background: #ffffff;
    border: 1px solid #e20b0b;
    color: #e20b0b;
}

.widget.unique-widget .widget-caption.sucess i.icon {
    background: #ffffff;
    border: 1px solid #74ba28;
    color: #74ba28;
}

.widget.unique-widget .widget-caption.warning i.icon {
    background: #ffffff;
    border: 1px solid #ff9800;
    color: #ff9800;
}

.widget .widget-detail h3 {
    margin-bottom: 4px;
    line-height: 1.3;
    font-size: 20px;
    margin-top: 0;
}

.widget .widget-detail {
    width: 100%;
    display: inline-block;
}

.widget-detail span {
    opacity: 0.6;
}

.widget .widget-caption .col-xs-8 {
    border-left: 1px solid lightgrey;
}

.widget-caption .col-xs-4.no-pad {
    padding-right: 0;
}

/*----------- Gallery Style ------------*/
ul.gallery-list {
    display: table;
    width: 100%;
}

ul.gallery-list li {
    width: 33.333333%;
    display: inline-block;
    padding: 0 5px;
    margin: 0;
}

ul.gallery-list li a img {
    border-radius: 4px;
}


.carousel-indicators button.thumbnail {
    flex: 1;
    display: flex;
    overflow: auto;
    width: 100px;
    height: 100px;
    min-height: min-content;
    min-width: fit-content;
}

.carousel-indicators button img.d-block.w-100 {
    max-height: 100px;
    height: 100px;
    object-fit: cover;
    padding: 5px;
    border-radius: 16px;
}

.carousel-indicators button.thumbnail:not(.active) {
    opacity: 0.7;
}

.carousel-indicators {
    position: static;
    flex: 1;
    display: flex;
    justify-content: start;
    width: 100%;
    overflow: auto;
    margin-left: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

@media screen and (min-width: 992px) {
    .carousel {
        max-width: 100%;
        margin: 0 auto;
    }

    .carousel-item img.d-block.w-100 {
        height: 500px;
        border-radius: 12px;
    }
}


/*==================================================
Login and Signup Popup
=======================================================*/

body.modal-open {
    padding-right: 0 !important;
}

.modal-open {
    overflow: initial;
}

.center {
    text-align: center;
}

a.subs-popup {
    width: 55px;
    height: 55px;
    display: inline-block;
    color: #ffffff;
    font-size: 20px;
    border-radius: 50%;
    line-height: 55px;
    position: absolute;
    top: 20px;
    right: 20px;
    background: #de6600;
    -webkit-box-shadow: 0px 0px 15px 2px rgba(0, 0, 0, 0.22);
    -moz-box-shadow: 0px 0px 15px 2px rgba(0, 0, 0, 0.22);
    box-shadow: 0px 0px 15px 2px rgba(0, 0, 0, 0.22);
}

@-moz-keyframes circlebeat {
    0% {
        -moz-transform: scale(0);
        opacity: 0.0;
    }

    25% {
        -moz-transform: scale(0.1);
        opacity: 0.1;
    }

    50% {
        -moz-transform: scale(0.5);
        opacity: 0.3;
    }

    75% {
        -moz-transform: scale(0.8);
        opacity: 0.5;
    }

    100% {
        -moz-transform: scale(1);
        opacity: 0.0;
    }
}

@-webkit-keyframes circlebeat {
    0% {
        -webkit-transform: scale(0);
        opacity: 0.0;
    }

    25% {
        -webkit-transform: scale(0.1);
        opacity: 0.1;
    }

    50% {
        -webkit-transform: scale(0.5);
        opacity: 0.3;
    }

    75% {
        -webkit-transform: scale(0.8);
        opacity: 0.5;
    }

    100% {
        -webkit-transform: scale(1);
        opacity: 0.0;
    }
}

.subs-popup .circlebeat {
    position: absolute;
    top: -10px;
    right: -10px;
    height: 75px;
    width: 75px;
    z-index: 10;
    border: 10px solid #ef0b13;
    border-radius: 70px;
    -moz-animation: circlebeat 1s ease-out;
    -moz-animation-iteration-count: infinite;
    -o-animation: circlebeat 1s ease-out;
    -o-animation-iteration-count: infinite;
    -webkit-animation: circlebeat 1s ease-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

.tab-pane img {
    margin: 0;
    display: block;
}

button.close {
    -webkit-appearance: none;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: +999;
    opacity: 1;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    line-height: 0;
}

.close:hover,
.close:focus {
    color: #ffffff;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=50);
    opacity: 1;
}

form {
    display: inline-block;
    width: 100%;
}

.modal textarea.form-control {
    height: 90px;
    padding: 15px;
}

.modal .form-control {
    height: 52px;
    padding: 0 15px;
    border: 0px;
    color: #727272;
    width: 100%;
    margin: 0 auto;
    /* margin-bottom: 12px; */
    font-size: 15px;
    border-radius: 2px;
    box-shadow: none;
}

.form-group {
    width: 100%;
}

.modal .form-control::-moz-placeholder {
    color: #6a7884;
    opacity: 1
}

.modal .form-control:-ms-input-placeholder {
    color: #6a7884
}

.modal .form-control::-webkit-input-placeholder {
    color: #6a7884;
    font-weight: 400;
    text-shadow: none;
}

.modal-content {
    padding: 0px 0px 35px 00px;
}

.modal h4 {
    color: #16162c;
    font-size: 30px;
    font-weight: normal;
}

.success-message {
    text-align: center;
    color: green;
}

.error-message {
    text-align: center;
    color: red;
}

.modal-body .nav-tabs>li {
    float: left;
    margin-bottom: -1px;
    width: 50%;
    text-align: center;
}

.modal-body .nav>li>a {
    position: relative;
    display: block;
    color: #95a2a7;
    padding: 18px 15px;
    background: #2a3646;
    border-radius: 0;
    margin: 0;
}

.modal-body .nav>li.active>a {
    background: #de6600;
    text-transform: uppercase;
    color: #ffffff !important;
}

.modal-body .nav>li>a:hover,
.modal-body .nav>li>a:focus {
    background: #de6600;
    border: 1px solid #0e9cf3;
    color: #ffffff !important;
}

.tab-content {
    padding: 2em 0 3em 0;
}

.modal-body .nav-tabs {
    margin-top: 0;
}

.modal-body .tab .nav-tabs {
    padding: 0;
}

.modal-body .tab .nav-tabs li a {
    border: none;
}

.modal-body .tab .nav-tabs li.active a,
.modal-body .tab .nav-tabs li.active a:hover {
    border: none;
}

/*================================================
Blog Page Style
===================================================*/

/*---------- Blog Style In Grid ----------*/
.blog-box {
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    margin-top: 25px;
    border-radius: 6px;
    overflow: hidden;
    background: #ffffff;
    transition: all ease 0.4s;
}

.blog-grid-box-img {
    height: 250px;
    max-height: 250px;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.blog-grid-box-content {
    padding: 20px 25px;
}

.blog-box:hover,
.blog-box:focus {
    -webkit-box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
    -moz-box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
    box-shadow: 0 10px 30px 0 rgba(58, 87, 135, 0.15);
}

.blog-avatar {
    display: table;
    margin: -58px auto 0 auto;
}

.blog-avatar.text-center img {
    max-width: 70px;
    border-radius: 50%;
    margin: 0 auto;
    border: 4px solid rgba(255, 255, 255, 1);
    margin-bottom: 5px;
    box-shadow: 0 2px 10px 0 #d8dde6;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    -moz-box-shadow: 0 2px 10px 0 #d8dde6;
}

/*---------- Blog Detail --------------*/
.short-blog {
    padding: 0;
    margin-bottom: 2em;
    border: 1px solid #d6e3ec;
}

figure.img-holder {
    position: relative;
}

.blog-post-date {
    position: absolute;
    bottom: 15px;
    left: 15px;
    padding: 5px 30px;
    border-radius: 2px;
    color: #ffffff;
    /* text-transform: capitalize; */
}

.blog-content {
    padding: 40px 25px;
    line-height: 1.8;
    color: #636d75;
}

.post-meta {
    color: #007a7a;
}

.post-meta>span {
    display: inline-block;
    line-height: 1;
    padding-right: 12px;
    margin-right: 10px;
    border-right: 1px solid #ebebeb;
}

.post-meta i {
    font-size: 17px;
    color: #929292;
    margin-right: 7px;
}

.post-tags strong {
    margin-right: 10px;
    color: #007a7a;
    font-size: 16px;
}

.post-tags {
    margin: 2em 0 0 0;
}

.post-tags a {
    color: #7b7b7b;
    background: #eaeef5;
    font-size: 14px !important;
    margin: 0 7px 10px 0;
    display: inline-block;
    padding: 4px 22px;
    -webkit-transition: all .3s ease-in-out 0s;
    -o-transition: all .3s ease-in-out 0s;
    transition: all .3s ease-in-out 0s;
    border-radius: 30px;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -ms-border-radius: 30px;
    -o-border-radius: 30px;
}

.full-blog .blog-content {
    padding: 40px 25px 20px 25px;
}

.full-blog .blog-content .bl-title {
    margin: 20px 0 12px;
    font-size: 22px;
}

.blog-footer-social {
    padding: 10px 0 0 0;
    border-top: 1px solid #e0ecf5;
    margin-top: 20px;
}

.b-detail-title {
    font-size: 20px;
    margin-bottom: 1.5rem;
}

ul.list-inline.social {
    padding: 0;
    margin: 0;
    float: none;
    display: inline-block;
}

ul.list-inline.social li {
    list-style: none;
    display: inline-block;
    padding: 0 10px;
}

ul.list-inline.social li i {
    width: 40px;
    height: 40px;
    background: #ffffff;
    border-radius: 50%;
    border: 1px solid #e0ecf5;
    color: #71818e;
    line-height: 38px;
    text-align: center;
    font-size: 16px;
    transition: all ease-in-out 0.4s;
}

ul.list-inline.social li i:hover,
ul.list-inline.social li i:focus {
    background: #eff2f5;
}

/*---------- Blogs Comment Style ----------*/
.comments {
    width: 100%;
    display: block;
    overflow: hidden;
    border-radius: 6px;
    margin-bottom: 40px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95, 0.08);
}

.comments-title {
    padding: 1em 1em 1em 1.5em;
    border-bottom: 1px solid #eaeff5;
}

.comments-title h4 {
    margin: 0;
}

.single-comment {
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 20px;
    padding-left: 80px;
    margin-top: 10px;
    padding-right: 15px;
}

.single-comment .single-comment {
    padding-left: 80px;
}

.single-comment .img-holder {
    left: 12px;
    border-radius: 50%;
    overflow: hidden;
    width: 50px;
    height: 50px;
    position: absolute;
    top: 0;
    box-shadow: 0 2px 10px 0 #d8dde6;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    -moz-box-shadow: 0 2px 10px 0 #d8dde6;
}

.single-comment .text-holder {
    border: 1px solid #eaeff5;
    padding: 20px;
}

.single-comment .text-holder .top {
    margin: 0 0 8px;
    overflow: hidden;
}

.rating.pull-right li {
    list-style: none;
    display: inline-block;
}

.rating.pull-right li i {
    font-size: 13px;
    margin-right: 3px;
    color: #636d75;
}

.rating.pull-right li i.active {
    color: #07b107;
}

.comments-form textarea.form-control {
    height: 150px;
}

.comments-form form {
    margin: 10px -15px;
}

/*------------ Blog Sidebar -------------*/
.side-list ul.side-blog-list li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 10px 5px 10px 5px;
    align-items: center;
}

.side-list.share-buttons {
    display: flex;
    justify-content: flex-end;
}

.blog-list-img {
    display: inline-block;
    width: 55px;
    max-width: 55px;
    overflow: hidden;
    height: 55px;
    border-radius: 4px;
    position: relative;
    margin: 0 10px 0 0;
    vertical-align: text-bottom;
}

.blog-post-meta {
    font-size: 90%;
}

.side-list .blog-post-meta span {
    float: none;
}

/*============================================
Icons Style
=============================================*/
.nav-tabs {
    border: 1px solid #e8ebef;
    border-left: none;
    border-right: none;
}

.nav-tabs>li>a:hover {
    border-color: #e8ebef #e8ebef #e8ebef;
    background: #e8ebef;
    border-radius: 0;
}

.nav-tabs-custom {
    border-right: 1px solid #e8ebef;
    border-left: 1px solid #e8ebef;
    border-bottom: 1px solid #e8ebef;
}

.nav-tabs>li>a {
    border-radius: 0;
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover {
    color: #555;
    cursor: default;
    background-color: #e8ebef;
    border: none;
    border-bottom-color: transparent;
}

.fontawesome-icon-list .col-md-3.col-sm-4 {
    padding: 10px 10px;
}

.bs-glyphicons li {
    width: 24.5%;
    height: 115px;
    padding: 10px;
    margin: 0 -1px -1px 0;
    font-size: 12px;
    line-height: 1.4;
    text-align: center;
    border: 1px solid #e8edef;
    display: inline-block;
}

.bs-glyphicons .glyphicon {
    margin-top: 5px;
    margin-bottom: 10px;
    font-size: 24px;
}

.bs-glyphicons .glyphicon-class {
    display: block;
    text-align: center;
    word-wrap: break-word;
}

.icon-container {
    width: 240px;
    padding: .7em 0;
    float: left;
    position: relative;
    text-align: left;
}

/*=========================================
Login And SignUp Style
==========================================*/
.modal {
    z-index: +99999;
    overflow-y: scroll;
}

.modal-content {
    border: none;
}

.modal-header {
    padding: 10px 15px;
    border-bottom: 1px solid #ebf1f7;
    display: flex;
    justify-content: space-between;
}

.modal-header h4 {
    float: left;
    font-size: 18px;
}

button.m-close {
    float: right;
    /* background: #eaeef5; */
    border: none;
    border-radius: 2px;
}
.utf-login_with {
    position: relative;
    text-align: center;
    margin: 15px 0px;
}
.utf-login_with .txt {
    display: inline-block;
    text-transform: uppercase;
    color: rgb(50, 50, 50);
    font-weight: 600;
    font-size: 14px;
    background: rgb(232, 232, 232);
    padding: 0px 10px;
    border-radius: 34px;
}
.login-tab-active {
    background: #de6600;
    width: 100%;
    padding: 10px;
    text-align: center;
    color: white;
    border-radius: 3px;
}
.login-tab {
    background: rgb(244, 244, 244);
    width: 100%;
    padding: 10px;
    text-align: center;
    color: black;
    border-radius: 3px;
}
.modal-body {
    position: relative;
    padding: 2em 2em 1em;
}

.wel-back {
    width: 100%;
    padding: 1em 0;
    text-align: center;
}

.connect-with {
    margin: 2em 0;
}

.connect-with ul {
    text-align: center;
}

.connect-with ul li {
    display: inline-block;
    padding: 0 12px;
}

.connect-with ul li a {
    width: 40px;
    height: 40px;
    display: inline-block;
    line-height: 40px;
    text-align: center;
    border: 2px solid #353950;
    border-radius: 4px;
}

.connect-with ul li.fb-ic a {
    color: #2b38a9;
    border: 2px solid #2b38a9;
}

.connect-with ul li.tw-ic a {
    color: #15a2e2;
    border: 2px solid #15a2e2;
}

.connect-with ul li.gp-ic a {
    color: #cc3636;
    border: 2px solid #cc3636;
}

.log-box {
    background: white;
    margin: 5em 0;
    padding: 2em 3em 4em;
    border-radius: 10px;
    border: 1px solid #e5e8ec;
    box-shadow: 0 0 10px 1px rgba(64, 65, 67, .05);
    -webkit-box-shadow: 0 0 10px 1px rgba(64, 65, 67, .05);
    -moz-box-shadow: 0 0 10px 1px rgba(64, 65, 67, .05)
}

.log-box span.input-group-addon {
    box-shadow: none;
    border: none;
    border-radius: 0;
    background: 0 0;
    border-bottom: 1px solid #dbe0e6;
    transition: all ease-in-out .4s
}

.log-box .form-control,
.log-box .form-control:hover,
.log-box .form-control:focus {
    border: none;
    transition: all ease-in-out .4s;
    background: 0 0;
    border-bottom: 1px solid #dbe0e6
}

.log-box .input-group {
    margin-bottom: 15px
}

.log-box h2 {
    margin-bottom: 25px;
    text-align: center;
}

/*====================================
 Bottom To top Scroll
=====================================*/
#back2Top {
    padding: 12px 10px;
    width: 40px;
    line-height: 40px;
    overflow: hidden;
    z-index: 999;
    display: none;
    cursor: pointer;
    position: fixed;
    bottom: 10px;
    right: 20px;
    text-align: center;
    font-size: 15px;
    border-radius: 50%;
    text-decoration: none;
}

#back2Top i {
    display: block;
}

#back2Top:hover {
    background-color: #f4f5f7;
    color: #2a3646;
}

/*===========================================
	New Pages Styles
=========================================*/
/*------------ Comon Style -------------*/
span.new-page-badge {
    display: inline-block;
    padding: 2px 4px;
    background: #34c112;
    color: #ffffff;
    font-size: 10px;
    border-radius: 2px;
    float: right;
}

.bootstrap-select button.btn.dropdown-toggle.btn-default:hover,
.bootstrap-select button.btn.dropdown-toggle.btn-default:focus {
    background-color: #fbfdff;
}

.slider-horizontal .tooltip {
    opacity: 1;
}

.tooltip-inner {
    background: #475363;
}

.tooltip.top .tooltip-arrow {
    border-top-color: #475363;
}

/*---------- Top Author Styles --------------*/
.tp-author-box {
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    margin-top: 20px;
    background: #fff;
    border: 1px solid #eaeff5;
    box-shadow: 0 0 10px 1px rgba(71, 85, 95, .08);
    -webkit-box-shadow: 0 0 10px 1px rgba(71, 85, 95, .08);
    -moz-box-shadow: 0 0 10px 1px rgba(71, 85, 95, .08);
}

.bg-image .tp-author-box {
    border: none;
}

.tp-author-box .author-cover-img {
    height: 110px;
    position: relative;
    overflow: hidden;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    background-size: cover;
    background-position: center;
    transition: transform .35s ease-out;
    transform: translate3d(0, 0, 0) scale(1);
    image-rendering: -webkit-optimize-contrast;
}

.author-cover-img:before {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #1d3054;
    content: "";
    opacity: 0.6;
}

.tp-author {
    position: relative;
    left: 25px;
    margin-top: -38px;
    width: 80px;
    border-radius: 50%;
    overflow: hidden;
    padding: 5px;
    background: rgba(255, 255, 255, 0.4);
    display: table;
    height: 80px;
}

.tp-author img {
    border-radius: 50%;
}

.tp-author-detail-box {
    width: 100%;
    display: table;
}

.tp-author {
    position: relative;
    left: 25px;
    margin-top: -38px;
    width: 80px;
    border-radius: 50%;
    overflow: hidden;
    padding: 5px;
    background: rgba(255, 255, 255, 0.4);
    display: table;
    height: 80px;
    float: left;
}

.tp-author-header {
    margin-left: 130px;
    top: 5px;
    position: relative;
    left: -10px;
}

.tp-author-header h4 {
    margin: 0;
    padding: 0 0 5px 0;
    font-size: 16px;
}

.tp-author-detail-box ul {
    padding: 0;
    margin: 0;
    list-style: none;
    line-height: 24px;
    margin-bottom: -7px;
}

.tp-author-detail-box ul li {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 12px;
}

.author-type {
    position: relative;
    height: 22px;
    display: flex;
    top: -1px;
    color: #fff;
    font-weight: 500;
    font-size: 12px;
    text-align: center;
    z-index: 10;
    font-weight: 500;
    border-radius: 2px;
    padding: 0 7px;
    margin: 0;
    overflow: hidden;
    line-height: 24px;
}

.author-type.normal-author {
    background: rgba(242, 17, 54, 0.12);
    color: #f21136;
}

.author-type.elite-author {
    background: rgba(255, 152, 0, 0.12);
    color: #ff9800;
}

.author-type.power-author {
    background: rgba(15, 183, 107, 0.12);
    color: #0fb76b;
}

.tp-author-basic-info {
    margin: 30px 0 0 0;
    padding: 0 25px;
    border-top: 1px solid #ebedf1;
}

.tp-author-basic-info ul {
    width: 100%;
    display: table;
}

.tp-author-basic-info li {
    list-style: none;
    display: inline-block;
    width: 25%;
    padding: 15px 0 10px;
}

.tp-author-basic-info li strong {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: #384454;
}

/*---------- Top Author Detail ------------*/
.main-author-header {
    display: table;
}

.mah-img-box {
    width: 100px;
    border-radius: 4px;
    overflow: hidden;
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: 5px;
}

.mah-detail-box {
    display: inline-block;
    vertical-align: top;
    text-align: left;
    margin-left: 20px;
    color: #ffffff;
}

.mah-detail-box h4 {
    color: #ffffff;
    margin-top: 0px;
    margin-bottom: 2px;
}

.mah-detail-box p {
    color: #ffffff;
    margin-bottom: 4px;
}

.mah-detail-box a.btn.btn-follow {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    padding: 8px 25px;
    font-size: 13px;
    letter-spacing: 1px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.4s;
}

.mah-detail-box a.btn.btn-follow:hover,
.mah-detail-box a.btn.btn-follow:focus {
    background: #ffffff;
    color: #353e4a;
}

.trending-list .listing-shot {
    margin-bottom: 0;
}

button.w3-button.w3-teal.w3-xlarge.w3-right {
    background: #fff;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 25px;
    position: fixed;
    bottom: 15px;
    right: 15px;
    border: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6
}

.w3-animate-right {
    position: relative;
    animation: animateright .4s
}

@keyframes animateright {
    from {
        right: -300px;
        opacity: 0
    }

    to {
        right: 0;
        opacity: 1
    }
}


/*------------ Classic Style -----------*/
.property_item {
    position: relative;
    border-radius: 10px;
    overflow: visible;
    /* Allow elements to extend outside */
    margin-bottom: 40px;
    /* Increased to accommodate the overflowing element */
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
    background-color: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    /* padding: 10px; */
    width: calc(25% - 16px); /* Subtracting the gap */
    margin: 8px;
}
@media (max-width: 1024px) {
    .property_item {
        width: calc(33.333% - 16px);
    }
 }
@media (max-width: 768px) {
    .property_item {
        width: calc(50% - 16px);
    }
 }

/* Add hover effect */
.property_item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: #d0d0d0;
}

.listing-thumb {
    position: relative;
    display: block;
    max-height: 200px;
    overflow: hidden;
    /* Keep this hidden for the state ribbon */
    border-radius: 6px 6px 0 0;
}


 img.img-responsive {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    /* border-radius: 7px; */
    max-height: 200px;
    /* Ensure image doesn't exceed container height */
    display: block;
    /* Remove any default inline spacing */
}
/* 
.listing-thumb:before {
    background: linear-gradient(to bottom, transparent 17%, #464c63);
    position: absolute;
    content: "";
    bottom: 0; */
    /*height: 100%;*/
    /* left: 0;
    right: 0;
    display: block;
    transition: all ease 0.4s;
    border-radius: 10px
} */

@media (min-width: 992px) {
    .property_item {
        transition: all 0.3s ease, transform 0.3s ease;
        transform: translateY(0);
    }

    .listing-thumb:hover {
        transition: all 0.3s ease, transform 0.3s ease;
        height: 100%;
    }

    .property_item:hover {
        transform: translateY(-10px);
    }

    .property_item:hover .listing-thumb:before {
        background: linear-gradient(to bottom, transparent 17%, #de6600);
    }
}

.listing-price-info {
    position: absolute;
    top:20px;
    right:5px;
    display: inline-block;
    border-radius: 50px;
    font-size: 16px;
    z-index: 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-content: right;
}

.listing-price-info span {
    display: inline-block;
    /* background: #ffffff; */
    background: #de6600;
    color: #ffffff !important;
    padding: 4px 12px;
    border-radius: 50px;
    font-size: 12px;
    /* right: 10px;*/
    color: #505667;
    box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.2);
    z-index: 0;
    text-align: center;
}

.image-listing-content {
    position: relative;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 10px 10px 10px;
    z-index: 10;
    color:black;
}

.listing-thumb .image {
    border-radius: 5px;
}

.image .state {
    color: #fff;
    float: left;
    position: absolute;
    transform: rotate(-45deg);
    left: -45px;
    top: 15px;
    text-align: center;
    width: 150px;
    font-size: 12px;
    margin: 0;
    color: #fff;
    font-weight: 600;
    line-height: 28px;
    border-radius: 0 20px 20px 0;
    z-index: 1;
}

a.tag_t {
    position: absolute;
    right: 20px;
    bottom: 20px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    color: #ffffff;
}

.tag_t i {
    margin-right: 10px;
    font-size: 18px;
}

.proerty_content {
    position: relative;
    text-align: center;
}

.proerty_text {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
}
.proerty_text {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
}
.countprice {
    justify-content: space-between;
}
@media (max-width: 767px) {
    .countprice {
        display: flex;
        flex-direction: column-reverse;
    }
    .proerty_text .captlize {
        font-size: 12px !important;
    }
}
.proerty_text div span {
    font-size: 11px;
    color: #6e778a;
}
.proerty_text .captlize {
    font-size: 18px;
    font-weight: 600;
    flex: 1;
    line-height: 1;
    margin-bottom: 5px;
}

.property_meta {
    display: block;
    margin: 1.5em 0 0rem;
}



.property_add {
    color: #6e778a;
}

.classical-list .btn {
    font-size: 16px;
    padding: 12px 22px;
}

.btn.btn-theme {
    background: #ff8b00;
    border-radius: 4px;
    margin-right: 10px;
    margin-bottom: 5px;
}

.btn.btn-theme-light {
    color: #ff8b00;
    background: rgba(255, 139, 0, 0.12);
    border-radius: 4px;
    margin-bottom: 5px;
}

.price-features-wrapper .listing-price-fx {
    flex: 1;
}

.inc-fleat {
    margin-left: 15px;
    color: #6e778a;
}

.list-fx-features {
    position: absolute;
    top: -30%;
    left: 0%;
    right: 0%;
    padding: 3px;
    z-index: 999;
    /* background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
    display: flex;
    justify-content: space-between;
    border-radius: 50px;
    align-items: center;
    max-height: 45px;
    text-align: left;
}
.ratingtag {
    font-weight:400; 
    font-size:9px; 
    border-radius:20px;
}
@media (max-width: 768px) {

    .list-fx-features {
        top: -24%;
    }
    .ratingtag {
        font-size:7px; 
    }
}

.share-btn {
    font-size: 11px;
    cursor: normal;
    background-color: #f0f0f0;
    border-radius: 30px;
    transition: all 0.4s;
    text-align: center;
    padding: 10px;
    width: 30px;

}

.share-btn i {
    display: block;
    /* Ensures the icon behaves properly */
}

.listing-card-info-icon {
    display: inline-block;
    padding-left: 18px;
    position: relative;
    vertical-align: top;
    flex: 0 0 50%;
    margin-bottom: 1.5rem;
    font-size: 14px;
}

.inc-fleat.inc-add:before {
    background: url(../img/add.svg);
    content: "";
    position: absolute;
    background-size: contain;
    width: 23px;
    height: 23px;
    top: 1px;
    left: 4px;
}

.inc-fleat.inc-call:before {
    background: url(../img/call.svg);
    content: "";
    position: absolute;
    background-size: contain;
    width: 23px;
    height: 23px;
    top: 1px;
    left: 4px;
}

.listing-card-info-icon img {
    margin-right: 5px;
    max-width: 18px;
}

.author-avater {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    display: table;
    margin: 0 auto;
    box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.3);
    overflow: hidden;
    top: -30px;
    position: relative;
}

.author-avater img {
    width: 100%;
    border-radius: 50%;
}

span.veryfied-author:before {
    content: "\e64c";
    display: inline-block;
    font-family: 'themify';
    width: 20px;
    height: 20px;
    color: #ffffff;
    background: #29af6a;
    border-radius: 50%;
    margin-left: 7px;
    top: 2px;
    font-size: 10px;
    line-height: 20px;
    text-align: center;
}

span.veryfied-author {
    position: relative;
}

.listing-footer-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    text-align: left;
    padding: 1.5rem;
}

.listing-cat {
    flex: 1;
    float: left;
}

a.cat-icon.cl-1 {
    color: red;
}

.cat-icon i {
    width: 32px;
    height: 32px;
    display: table;
    background: red;
    border-radius: 50%;
    text-align: center;
    line-height: 32px;
    color: #ffffff;
    margin-right: 5px;
    float: left;
}

span.more-cat {
    display: inline-block;
    width: 32px;
    height: 32px;
    text-align: center;
    background: #ffffff;
    border: 2px solid #bdc4d6;
    border-radius: 50%;
    line-height: 28px;
    margin-left: 1rem;
    color: #707988;
    font-size: 15px;
}

a.cat-icon.cl-1 {
    color: #6d7a8a;
    font-weight: 600;
}

.listing-footer-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    text-align: left;
    padding: 1rem 1.5rem;
    border-top: 1px dashed #e8edfb;
}

.list-rate {
    position: absolute;
    left: 20px;
    bottom: 20px;
    background: #34a853;
    padding: 4px 12px;
    border-radius: 3px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.3;
}

.place-status {
    color: #12b96c;
}

.place-status.closed {
    color: #e42f2f;
}

/*----------- New Grid Listing Grid -----------*/
.modern-list {
    background: #ffffff;
    position: relative;
    display: block;
    border-radius: 14px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 0 20px 0 rgba(62, 28, 131, 0.1);
    -webkit-box-shadow: 0 0 20px 0 rgba(62, 28, 131, 0.1);
    -moz-box-shadow: 0 0 20px 0 rgba(62, 28, 131, 0.1);
}

.list-slide-box .modern-list {
    margin-bottom: 0;
}

.modern-list .popular {
    position: absolute;
    right: 30px;
    top: 30px;
}

.modern-list .hot {
    position: absolute;
    right: 30px;
    top: 30px;
}

.modern-list .new {
    position: absolute;
    right: 30px;
    top: 30px;
}

.grid-category-thumb {
    display: table;
    width: 100%;
    min-height: 200px;
    padding: 10px;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.grid-category-thumb img {
    border-radius: 10px;
}

.modern-list-content {
    position: relative;
    padding: 5px 20px 15px;
    display: table;
    width: 100%;
}

.lst-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 3px;
}

.list-rates i {
    color: #6e778a;
    font-size: 15px;
}

.list-rates i.filled {
    color: #ff8000;
}

.list-rates a {
    font-size: 16px;
    margin-left: 10px;
    color: #6e778a;
}

.overlay-cate {
    position: relative;
    height: 100%;
    display: block;
}

.overlay-cate:before {
    content: "";
    position: absolute;
    background: linear-gradient(to bottom, transparent 7%, #1a1d2b);
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: block;
    opacity: 1;
    border-radius: 6px;
}

.modern-list .property_meta {
    display: block;
    margin: 0;
    position: absolute;
    left: 30px;
    bottom: 30px;
}

.modern-list .listing-card-info-icon {
    font-size: 15px;
}

.modern-list .listing-card-info-icon {
    width: 100%;
    flex: 0 0 100%;
    margin-bottom: 1rem;
}

.modern-list .inc-fleat {
    margin-left: 15px;
    color: #ffffff;
}

.modern-list .inc-fleat.inc-add:before {
    background: url(../img/light-add.svg);
    content: "";
    position: absolute;
    background-size: contain;
    width: 23px;
    height: 23px;
    top: 1px;
    left: 4px;
}

.modern-list .inc-fleat.inc-call:before {
    background: url(../img/light-call.svg);
    content: "";
    position: absolute;
    background-size: contain;
    width: 23px;
    height: 23px;
    top: 1px;
    left: 4px;
}

.modern-list-cat {
    background: #f5203e;
    position: absolute;
    left: 30px;
    bottom: 104px;
    padding: 5px 16px;
    color: #ffffff;
    font-size: 16px;
    border-radius: 50px;
}

.modern-list.ml-2 .author-avater {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    display: table;
    margin: 0 auto;
    box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.3);
    overflow: hidden;
    top: -40px;
    position: absolute;
    right: 45px;
}

.modern-list.ml-2 .lst-title a {
    color: #ffffff;
}

.modern-list.ml-2 .list-rates a {
    font-size: 16px;
    margin-left: 10px;
    color: #ffffff;
}

.modern-list.ml-2 .list-rates {
    margin-bottom: 4px;
}

.modern-list.ml-2 .list-rates i {
    color: #ffffff;
}

.modern-list.ml-2 .list-rates i.filled {
    color: #ff8000;
}

.property_item .medium {
    background: #ff8d00;
}

.property_item .good {
    background: #6ebd17;
}

.property_item .great {
    background: #12b96c;
}

.property_item .poor {
    background: #f5225c;
}

.list-badge {
    background-color: #333;
    float: left;
    position: absolute;
    transform: rotate(45deg);
    right: -64px;
    top: 22px;
    text-align: center;
    width: 200px;
    font-size: 12.5px;
    margin: 0;
    z-index: 999;
    color: #fff;
    font-weight: 500;
    line-height: 28px;
}

.list-badge.now-open {
    background-color: #12b94d;
}

.list-badge.now-close {
    background-color: #e42626;
}


/* ------------ new Banner * Air BNB Style ------------*/
.air-bnb-form {
    background: #ffffff;
    max-width: 530px;
    float: left;
    border-radius: 10px;
    box-shadow: 0 0 20px 0 rgba(62, 28, 131, 0.1);
    padding: 4rem 3rem;
    text-align: left;
}

.air-bnb-form h2 {
    font-weight: 600;
}

.banner .air-bnb-form p {
    color: #545665;
    line-height: 1.7;
    font-size: 17px;
}

.banner-caption .air-bnb-form .form-control {
    border: 1px solid #d3d7e0;
    border-radius: 3px;
}

.air-bnb-form i.banner-icon {
    left: 27px !important;
}

/*------------ New Category ------------*/
.small-features-box-colors {
    background-color: #f6f8fb;
    border-radius: 6px;
    padding: 35px 15px;
    text-align: center;
    -webkit-transition: .25s ease-in-out;
    transition: .25s ease-in-out;
    margin-bottom: 30px;
}

.small-features-box-colors .sfb-icon {
    display: table;
    width: 70px;
    height: 70px;
    text-align: center;
    line-height: 70px;
    border-radius: 50%;
    margin: 20px auto;
    font-size: 30px;
    background: #ffffff;
}

.slick-dots li.slick-active button:before {
    opacity: .75;
    color: #ff431e;
    background: rgba(255, 67, 30, 0.2);
    border-radius: 50%;
}

.slick-dots li button:before {
    -moz-osx-font-smoothing: grayscale;
    background: rgba(84, 96, 117, 0.2);
    border-radius: 50%;
}

.tag-section {
    background-size: cover !important;
    background-position: center !important;
}

.lp-tag-wrap {
    position: relative;
    max-width: 610px;
    margin: 3rem 0;
    background: #ffffff;
    padding: 3em;
    border-radius: 6px;
}

.lp-tag-wrap h2 {
    font-weight: 600;
    line-height: 1.3;
}

.lp-tag-wrap p {
    font-size: 15px;
    line-height: 1.7;
}

.modal-content .form-group {
    display: inherit;
}

/*----------- Switchbar -------------*/
button.w3-bar-item.w3-button.w3-large {
    border: none;
    width: 100%;
    padding: 10px 0;
    font-size: 18px;
    color: #fff
}

ul#styleOptions {
    margin: 20px 0;
    padding: 0
}

.title-logo {
    padding: 20px 12px
}

.title-logo h4 {
    font-size: 17px;
    margin: 5px 0
}

ul#styleOptions li {
    list-style: none;
    display: inline-block;
    margin: 5px
}

a.cl-box {
    width: 40px;
    height: 40px;
    border-radius: 5px;
    display: block;
    margin: 0 auto
}

.w3-sidebar {
    height: 100%;
    width: 250px;
    background-color: #fff;
    position: fixed !important;
    top: 0;
    z-index: +107555;
    overflow: auto;
    border-left: 1px solid #eaeff5;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6
}

.alabama {
    background: #af002a
}

.amaranth {
    background: #d3212d
}

.alizarin {
    background: #E32636
}

.imperial-blue {
    background: #6E6Ef9
}

.university {
    background: #c00
}

.cerulean {
    background: #1dacd6
}

.lilac {
    background: #d891Ef
}

.bronze {
    background: #cd7f32
}

.viridian-green {
    background: #009698
}

.yellow {
    background: #ffba00
}

.kelly-green {
    background: #4cbb17
}

.lava {
    background: #cf1020
}

.violet {
    background: violet
}

.cadmium {
    background: #Ed872d
}

.rich-carmine {
    background: #d70040
}

.ball-blue {
    background: #21abcd
}

.charcoal {
    background: #36454f
}

.lime-green {
    background: #32cd32
}

.cg-blue {
    background: #007aa5
}

.crimson {
    background: #dc143c
}

.cyan-cornflower {
    background: #188bc2
}

.dark-cyan {
    background: #008b8b
}

.dark-pink {
    background: #E75480
}

.debian-red {
    background: #d70a53
}

.bleu-de-france {
    background: #318cE7
}

.fuchsia {
    background: #c154c1
}

.forest-green {
    background: #228b22
}

.go-green {
    background: #00ab66
}

.azure {
    background: #007fff
}

.american-rose {
    background: #ff033E
}

.amber {
    background: #ff7E00
}

.green-pantone {
    background: #00ad43
}

.orange-pantone {
    background: #ff5800
}

.burnt-orange {
    background: #c50
}

.blueberry {
    background: #4f86f7
}

.magenta-violet {
    background: #553592
}

.blue-green {
    background: #0d98ba
}

.blue-munsell {
    background: #0093af
}

.coquelicot {
    background: #ff3800
}

.light-red-btn {
    background: #e8edf1;
    border: 1px solid #de6600;
}

.light-gray-btn:hover,
.light-gray-btn:focus {
    color: #ffffff;
    background: #78909C;
    border: 1px solid #78909C;
}

.light-red-btn:hover,
.light-red-btn:focus {
    color: #ffffff;
    background: #de6600;
    border: 1px solid #de6600;
}

.filter-button {
    background-color: #fff;
    border-radius: 4px;
    transform: translate3d(0, 0, 0);
    z-index: 90;
    position: relative;
    color: #495369;
    font-size: 14px;
    padding: 8px 8px 8px 8px;
    text-align: left;
    font-weight: 600;
    border: thin solid #dde6ef;
    /*	padding:0 12px 32px 20px;
    border: 1px solid #eaeff5;
    box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
    -moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);*/
}

.filter-button-header {
    margin-top: 5px;
}

.filter-button-header p>i {
    margin-top: 5px;
    margin-right: 5px;
}

.filter-button-body {
    padding: 20px 0 0 0;
}

/* Enhanced styling for contact section */
/* .contact-item {
    margin-bottom: 15px;
} */

.contact-item strong {
    font-weight: 600;
    color: #333;
    margin-right: 5px;
}

.social-links strong {
    font-weight: 600;
    color: #333;
    margin-right: 5px;
}

.contact-button {
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 8px 15px;
    background-color: #f0f2f5;
    border-radius: 6px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.contact-button:hover {
    background-color: #e6e9ed;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: #de6600;
}

.contact-button i {
    margin-right: 8px;
}

.social-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 10px;
}

.social-button {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    border-radius: 6px;
    text-decoration: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.social-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    color: white;
}

.social-button i {
    margin-right: 8px;
    font-size: 18px;
}

.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.facebook {
    background-color: #3b5998;
}

.whatsapp {
    background-color: #25d366;
}

@media (max-width: 768px) {
    .social-links {
        flex-direction: column;
        gap: 8px;
    }

    .social-button,
    .contact-button {
        /* width: 100%; */
        justify-content: center;
    }
}

/* Make tabs horizontal on mobile */
@media (max-width: 767px) {

    .tab .nav-tabs,
    .nav-tabs {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        /* Add smooth scrolling */
        padding-bottom: 5px;
        /* Add some padding to avoid cut-off */
    }

    .tab .nav-tabs li,
    .nav-tabs li {
        float: none;
        display: inline-block;
        width: auto;
        margin-bottom: 0;
        border-right: none;
    }

    .tab .nav-tabs li a,
    .tab .nav-tabs li button,
    .nav-tabs li a,
    .nav-tabs li button {
        padding: 10px 15px;
        font-size: 14px;
    }

    /* Hide scrollbar but keep functionality */
    .tab .nav-tabs::-webkit-scrollbar,
    .nav-tabs::-webkit-scrollbar {
        height: 3px;
    }
}

@media (max-width: 767px) {
    .swal2-popup {
        width: 90% !important;
        max-width: 90% !important;
        margin: 0 auto !important;
    }
}

/* Line Mode Specific CSS with unique class names */
.line-property-item {
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.line-property-item:hover {
    transform: translateY(-5px);
}

.line-listing-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.line-card-container {
    display: flex;
    background-color: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.line-card-container:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.line-image-container {
    position: relative;
    width: 200px;
    height: 100%;
    flex-shrink: 0;
}

.line-property-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px 0 0 5px;
}

.line-status-badge {
    color: #fff;
    float: left;
    position: absolute;
    transform: rotate(-45deg);
    left: -45px;
    top: 15px;
    text-align: center;
    width: 150px;
    font-size: 12px;
    margin: 0;
    color: #fff;
    font-weight: 600;
    line-height: 28px;
    border-radius: 0 20px 20px 0;
    z-index: 1;
}


.line-status-open {
    background-color: #28a745;
}

.line-type-badge {
    position: absolute;
    top: 20px;
    right: 0px;
    display: inline-block;
    border-radius: 50px;
    font-size: 16px;
    background-color: #e67e22;
    color: white;
    font-size: 10px;
    font-weight: 400;
    padding: 5px 10px;
}

.line-content-container {
    padding: 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    min-height: 150px;
}

.line-content-top {
    margin-bottom: 10px;
}

.line-property-title {
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.line-location-container,
.line-phone-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.line-money-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    border: 1px solid #00796b;
    border-radius: 5px;
    padding: 3px;
}

.line-icon-location,
.line-icon-phone {
    color: #de6600;
    margin-right: 5px;
}
.line-icon-money {
    color: #00796b;
    margin-right: 5px;
}

.line-location-text,
.line-phone-text,
.line-money-text {
    font-size: 14px;
    color: #777;
}

.line-description {
    margin-bottom: 10px;
    font-size: 14px;
    color: #777;
    line-height: 1.4;
}

.line-content-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.line-rating-container {
    display: flex;
    align-items: center;
}

.line-stars {
    margin-right: 5px;
}

.line-star-filled {
    color: #ffd700;
}

.line-star-empty {
    color: #ddd;
}

.line-rating-count {
    font-size: 14px;
    color: #777;
}

.line-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }

.line-price-range {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 4px 8px;
    margin-right: 10px;
    font-size: 13px;
    color: #555;
}

.line-share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.line-share-button:hover {
    background-color: #e9ecef;
}

/* Responsive styles */
@media (max-width: 767px) {
    .line-card-container {
        flex-direction: column;
    }

    .line-image-container {
        width: 100%;
        height: 180px;
    }

    .line-property-image {
        border-radius: 5px 5px 0 0;
    }

    .line-content-container {
        padding: 15px;
    }
}

.btn-inactive {
    background: #e8edf1;
    border: 1px solid #e5eaef;
}

.btn-inactive:hover {
    border: 1px solid #de6600;
    color: #de6600
}

.position-cl {
    background: #de6600;
    color: white;
    font-weight:500;
    padding: 10px 8px;
    border-radius: 10px;

}
  
  .utf_box {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    padding: 16px 8px;
    text-align: center;
    font-weight: 600;
    box-shadow: 0px 0px 16px 0px rgba(2, 2, 2, 0.1);
    -webkit-box-shadow: 0px 0px 16px 0px rgba(2, 2, 2, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .categorieatag {
    background: rgba(255, 255, 255, 0.07);
    position: relative;
    width: 150px;
    height: 120px; /* Increased height to accommodate two lines */
    display: block;
    overflow: hidden;
    color: #ffffff;
    border-radius: 6px;
    padding: 7px;
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out;
  }
  
  .utf_box i {
    font-size: 26px;
    margin-bottom: 8px;
    display: block;
  }
  
  .category-title {
    font-size: 16px;
    color: white !important;
    line-height: 1.2;
    height: 40px; /* Fixed height for text area */
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .categorieatag:hover {
    background: #de6600;
  }
  .login-with-google-btn {
    transition: background-color 0.3s, box-shadow 0.3s;
    padding: 10px 16px 10px 42px;
    border: none;
    border-radius: 24px;  /* Rounded corners like in your image */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    color: #3c4043;
    font-size: 14px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMTcuNiA5LjJsLS4xLTEuOEg5djMuNGg0LjhDMTMuNiAxMiAxMyAxMyAxMiAxMy42djIuMmgzYTguOCA4LjggMCAwIDAgMi42LTYuNnoiIGZpbGw9IiM0Mjg1RjQiIGZpbGwtcnVsZT0ibm9uemVybyIvPjxwYXRoIGQ9Ik05IDE4YzIuNCAwIDQuNS0uOCA2LTIuMmwtMy0yLjJhNS40IDUuNCAwIDAgMS04LTIuOUgxVjEzYTkgOSAwIDAgMCA4IDV6IiBmaWxsPSIjMzRBODUzIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNNCAxMC43YTUuNCA1LjQgMCAwIDEgMC0zLjRWNUgxYTkgOSAwIDAgMCAwIDhsMy0yLjN6IiBmaWxsPSIjRkJCQzA1IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNOSAzLjZjMS4zIDAgMi41LjQgMy40IDEuM0wxNSAyLjNBOSA5IDAgMCAwIDEgNWwzIDIuNGE1LjQgNS40IDAgMCAxIDUtMy43eiIgZmlsbD0iI0VBNDMzNSIgZmlsbC1ydWxlPSJub256ZXJvIi8+PHBhdGggZD0iTTAgMGgxOHYxOEgweiIvPjwvZz48L3N2Zz4=);
    background-color: white;
    background-repeat: no-repeat;
    background-position: 12px center;  /* Changed to center vertically */
    display: flex;  /* Added flex display */
    align-items: center;  /* Vertically center the content */
    height: 40px;  /* Fixed height */
    width: auto;
    line-height: 1;  /* Reset line height */
    text-align: center;
  }
  
  .login-with-google-btn:hover {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    background-color: #f8f9fa;
  }
  
  .login-with-google-btn:active {
    background-color: #f1f3f4;
  }
  
  .login-with-google-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.15);
  }
  .mobile-user-header {
    padding: 5px 15px 0 20px;
  }

  /* Enhanced counter styles */
  .listing-title-bar, .counter-container {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    font-size: 12px;
    margin-top: 5px;
}

.counter-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6b7280;
}
.counter-item-alt {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6b7280;
    border: #6b7280 solid 1px;
    border-radius: 3px;
    padding: 5px 10px;
}

/* Modify the previous fix to prevent horizontal scrolling while maintaining proper alignment */
html, body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
}

/* Fix for container alignment on mobile */
@media (max-width: 767px) {
    .container, .container-fluid {
        padding-left: 5px;
        padding-right: 5px;
        width: 100%;
        max-width: 100%;
    }
    
    /* Fix for search results and product view alignment */
    .property-grid, 
    .listing-shot,
    .listing-detail,
    .detail-wrapper,
    .col-md-12,
    .row {
        padding-left: 0;
        padding-right: 0;
        margin-left: 0;
        margin-right: 0;
        width: 100%;
    }
    
    /* Ensure content is centered */
    .text-center-xs {
        text-align: center !important;
    }
    
    /* Fix for any negative margins that might be causing issues */
    .mob-extra-mrg {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

        .bg-vamiyi-orange {
            background-color: var(--vamiyi-orange);
        }

        .text-vamiyi-orange {
            color: var(--vamiyi-orange);
        }

        .btn-outline-secondary {
            border-color: var(--vamiyi-orange);
            color: var(--vamiyi-orange);
        }

        .btn-outline-secondary:hover {
            background-color: rgba(255, 107, 0, 0.1);
            color: var(--vamiyi-orange);
        }

        /* Stepper styles */
        .stepper-wrapper {
            margin-bottom: 30px;
        }

        .stepper-progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .stepper-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
            flex: 1;
        }

        .stepper-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid var(--vamiyi-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: var(--vamiyi-gray);
            cursor: pointer;
        }

        .stepper-circle.active {
            background-color: var(--vamiyi-orange);
            border-color: var(--vamiyi-orange);
            color: #de6600;
        }

        .stepper-circle.completed {
            background-color: rgba(255, 107, 0, 0.2);
            border-color: var(--vamiyi-orange);
            color: var(--vamiyi-orange);
        }

        .stepper-circle:disabled {
            cursor: not-allowed;
        }

        .stepper-label {
            margin-top: 8px;
            font-size: 12px;
            color: var(--vamiyi-gray);
        }

        .stepper-label.active {
            color: var(--vamiyi-orange);
            font-weight: 500;
        }

        .stepper-line {
            position: absolute;
            height: 2px;
            background-color: var(--vamiyi-gray);
            width: calc(100% - 80px);
            top: 20px;
            left: calc(50% + 20px);
        }

        .stepper-line.completed {
            background-color: var(--vamiyi-orange);
        }

        .card {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 5%;
        }

        .card-header {
            border-bottom: none;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .progress-bar {
            transition: width 0.3s ease;
        }

        /* Hide steps that are not active */
        .step-content {
            transition: all 0.3s ease;
        }

        .d-none {
            display: none;
        }