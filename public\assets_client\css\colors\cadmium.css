/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #Ed872d !important;
}
.heading h2 span {
    color:#Ed872d;
}
.theme-cl {
    color:#Ed872d;
}
.theme-bg {
    background:#Ed872d;
}
span.like-listing i {
    background:#Ed872d;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#Ed872d;
}
.feature-box span {
    background:#Ed872d;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color: #Ed872d;
    border-color:#Ed872d;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#Ed872d;
    border-color:#Ed872d;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#Ed872d;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#Ed872d;
	border-color:#Ed872d;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#Ed872d;
}
span.category-tag {
    color:#Ed872d;
    border: 1px solid #Ed872d;
}
/*---prices---*/
.active .package-header {
    background:#Ed872d;
}
button.btn.btn-package {
    background:#Ed872d;
}
/*----button colors---*/
.theme-btn {
    background:#Ed872d;
    border: 1px solid #Ed872d;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#Ed872d;
    border: 1px solid #Ed872d;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #Ed872d;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#Ed872d;
	border-color:#Ed872d;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(237, 135, 45,0.1);
    color:#Ed872d;
    border-radius: 50px;
    border: 1px solid #Ed872d;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(237, 135, 45,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #Ed872d;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#Ed872d;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#Ed872d;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#Ed872d;
	border:1px solid #Ed872d;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#Ed872d;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#Ed872d;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#Ed872d;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#Ed872d;
}
a.btn.listing-btn {
    background:#Ed872d;
    border: 1px solid #Ed872d;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#Ed872d;
}
.title-content a{
	color:#Ed872d;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#Ed872d;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#Ed872d;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#Ed872d;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#Ed872d;
    border: 1px solid #Ed872d;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#Ed872d;
    background-color: rgba(237, 135, 45,0.12);
    border-color:#Ed872d;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#Ed872d;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#Ed872d;
}
.layout-option a:hover, .layout-option a:focus {
    color:#Ed872d;
}
.edit-info .btn {
    border: 1px solid #Ed872d;
    color:#Ed872d;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#Ed872d;
	background:#Ed872d;
}
ul.social-info.info-list li i {
    color:#Ed872d;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#Ed872d;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#Ed872d;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #Ed872d;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #Ed872d;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#Ed872d;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#Ed872d;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#Ed872d;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#Ed872d;
    border-bottom: 2px solid #Ed872d;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#Ed872d;
    border-bottom: 2px solid #Ed872d;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #Ed872d;
    background:#Ed872d;
}

.footer-copyright p a {
    color: #Ed872d;
}
.footer-social li a:hover i {
    background: #Ed872d;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #Ed872d;
    border: 1px solid #Ed872d;
}
.small-list-detail p a, p a {
    color: #Ed872d;
}
.quote-card::before {
    color:#Ed872d;
}
.quote-card cite {
    color:#Ed872d;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #Ed872d;
}
.service-box:before {
    border-left: 1px solid #Ed872d;
    border-right: 1px solid #Ed872d;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #Ed872d;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #Ed872d;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #Ed872d;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#Ed872d;
}
.service-box:after {
    border-bottom: 1px solid #Ed872d;
    border-top: 1px solid #Ed872d;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#Ed872d;
}
.range-slider .slider-selection {
    background:#Ed872d;
}
.range-slider .slider-handle.round {
    border: 2px solid #Ed872d;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #Ed872d;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#Ed872d !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#Ed872d !important;
}
}