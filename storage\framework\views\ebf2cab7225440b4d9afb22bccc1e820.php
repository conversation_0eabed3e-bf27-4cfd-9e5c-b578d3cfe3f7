<?php $__env->startSection('title', 'Mon entreprise'); ?>

<?php $__env->startSection('content'); ?>


    <?php
        $breadcrumbs = [['route' => 'accueil', 'label' => 'Accueil'], ['label' => 'Mon entreprise']];
    ?>

    <?php if (isset($component)) { $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcumb','data' => ['backgroundImage' => ''.e(asset('assets_client/img/banner/image-2.jpg')).'','showTitle' => true,'showSearchButton' => true,'title' => 'Mon entreprise','breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['backgroundImage' => ''.e(asset('assets_client/img/banner/image-2.jpg')).'','showTitle' => true,'showSearchButton' => true,'title' => 'Mon entreprise','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $attributes = $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $component = $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>

    <section class="list-detail">
        <div class="container">
            <div class="row bott-wid">
                <!-- Start: Listing Detail Wrapper -->
                <div class="col-md-8 col-sm-8">
                    <div class="detail-wrapper">
                        <div class="detail-wrapper-body">
                            <div class="listing-title-bar">
                                <h3> <?php echo e($entreprise->nom); ?>

                                    <span class="mrg-l-5 category-tag">
                                        <a class="btn-theme" href="<?php echo e(route('public.my-business.edit')); ?>">Modifier</a>
                                    </span>
                                </h3>
                                <div>
                                    <a href="javascript:void(0)" class="listing-address">
                                        <i class="ti-location-pin mrg-r-5"></i>
                                        <?php echo e($entreprise->adresse_complete); ?>

                                    </a>
                                    <br>
                                    <?php if($entreprise->telephone): ?>
                                        <a href="tel:<?php echo e($entreprise->telephone); ?>">
                                            <i class="ti-mobile"></i>&nbsp;
                                            <?php echo e($entreprise->telephone); ?>

                                        </a>
                                        <br>
                                    <?php endif; ?>

                                    <?php if($entreprise->email): ?>
                                        <a href="mailto:<?php echo e($entreprise->email); ?>">
                                            <i class="ti-email"></i>&nbsp;
                                            <?php echo e($entreprise->email); ?>

                                        </a>
                                        <br>
                                    <?php endif; ?>

                                    <?php if($entreprise->whatsapp): ?>
                                        <a href="https://api.whatsapp.com/send?phone=<?php echo e($entreprise->whatsapp); ?>" target="_blank">
                                            <i class="fa-brands fa-whatsapp"></i>&nbsp;
                                            <?php echo e($entreprise->whatsapp); ?>

                                        </a>
                                        <br>
                                    <?php endif; ?>

                                    <?php if($entreprise->instagram): ?>
                                        <a href="<?php echo e($entreprise->instagram); ?>" target="_blank">
                                            
                                            <i class="fa-brands fa-instagram"></i>&nbsp;
                                            <?php echo e($entreprise->instagram); ?>

                                        </a>
                                        <br>
                                    <?php endif; ?>

                                    <?php if($entreprise->facebook): ?>
                                        <a href="<?php echo e($entreprise->facebook); ?>" target="_blank">
                                            
                                            <i class="fa-brands fa-facebook"></i>&nbsp;
                                            <?php echo e($entreprise->facebook); ?>

                                        </a>
                                        <br>
                                    <?php endif; ?>

                                    <?php if($entreprise->site_web): ?>
                                        <a href="<?php echo e($entreprise->site_web); ?>" target="_blank">
                                            <i class="fa fa-globe"></i>&nbsp;
                                            <?php echo e($entreprise->site_web); ?>

                                        </a>
                                        <br>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-wrapper">
                        <div class="detail-wrapper-header">
                            <h4>Description</h4>
                        </div>
                        <div class="detail-wrapper-body">
                            <?php if($entreprise->description): ?>
                                <p><?php echo e($entreprise->description); ?></p>
                            <?php else: ?>
                                <p>Aucune description n'est disponible pour cette entreprise.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="detail-wrapper">
                        <div class="detail-wrapper-header">
                            <h4>Localisation</h4>
                        </div>
                        <div class="detail-wrapper-body p-0 px-4 pb-4">
                            <div class="side-list">
                                <ul>
                                    <li><?php echo e($entreprise->adresse_complete); ?></li>
                                    <li>
                                        <div id="map" class="full-width" style="height:400px;"></div>
                                    </li>
                                </ul>
                            </div>

                            
                        </div>
                    </div>
                </div>
                <!-- End: Listing Detail Wrapper -->

                <!-- Sidebar Start -->
                <div class="col-md-4 col-sm-12">
                    <div class="sidebar">

                        <!-- Start: Opening hour -->
                        <div class="widget-boxed">
                            <div class="widget-boxed-header">
                                <h4><i class="ti-time padd-r-10"></i>Heures d'ouverture</h4>
                            </div>
                            <div class="widget-boxed-body">
                                <div class="side-list">
                                    <ul>
                                        <?php $__currentLoopData = $entreprise->heure_ouvertures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $ouverture): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($ouverture == 'Fermé'): ?>
                                                <li><?php echo e($key); ?> <span
                                                        class="text-danger"><?php echo e($ouverture); ?></span></li>
                                            <?php else: ?>
                                                <li><?php echo e($key); ?> <span><?php echo e($ouverture); ?></span></li>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <!-- End: Opening hour -->

                        <!-- Start: Listing Gallery -->
                        
                        <!-- End: Listing Gallery -->

                        <!-- Start: Latest Listing -->
                        <div class="widget-boxed">
                            <div class="widget-boxed-header">
                                <h4><i class="ti-check-box padd-r-10"></i>Derniéres annonces</h4>
                            </div>
                            <div class="widget-boxed-body padd-top-5">
                                <div class="side-list">
                                    <ul class="listing-list">
                                        <?php $__currentLoopData = $annonces; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annonce): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <a href="<?php echo e(route('show', $annonce->slug)); ?>" title="Listing">
                                                    <div class="listing-list-img">
                                                        <?php if($annonce->image): ?>
                                                            <img src="<?php echo e(asset('storage/' . $annonce->imagePrincipale->chemin)); ?>"
                                                                class="img-responsive" alt="">
                                                        <?php else: ?>
                                                            <img src="http://via.placeholder.com/80x80"
                                                                class="img-responsive" alt="">
                                                        <?php endif; ?>
                                                    </div>
                                                </a>
                                                <div class="listing-list-info">
                                                    <h5><a href="<?php echo e(route('show', $annonce->slug)); ?>"
                                                            title="Listing"><?php echo e($annonce->titre); ?></a></h5>
                                                    <div class="listing-post-meta">
                                                        <span
                                                            class="updated"><?php echo e(date('d-m-Y', strtotime($annonce->created_at))); ?></span>
                                                        | <a href="<?php echo e(route('entreprise.show', $annonce->entreprise->slug)); ?>"
                                                            rel="tag"><?php echo e($annonce->type); ?></a>
                                                    </div>
                                                </div>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php if(empty($annonces)): ?>
                                            <p>
                                                Aucune annonce n'est disponible pour cette entreprise.
                                            </p>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <!-- End: Latest Listing -->
                    </div>
                </div>
                <!-- End: Sidebar Start -->
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script>
        var mymap;
        var marker;
        var mapHelper;

        <?php if($entreprise->latitude && $entreprise->longitude): ?>
            var lat = <?php echo e($entreprise->latitude); ?>;
            var lng = <?php echo e($entreprise->longitude); ?>;

            // Initialize Google Maps using global system
            mapHelper = initGoogleMap('map', {
                center: { lat: lat, lng: lng },
                zoom: 12,
                clickable: false
            }, function(helper, map) {
                mymap = map;
                helper.setMarker(lat, lng, 12);
                marker = helper.marker;
            });
        <?php else: ?>
            // Initialize Google Maps using global system
            mapHelper = initGoogleMap('map', {
                center: { lat: 8.6195, lng: 0.8248 },
                zoom: 6,
                clickable: false
            }, function(helper, map) {
                mymap = map;
            });
        <?php endif; ?>
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.public.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/public/user/company/show.blade.php ENDPATH**/ ?>