<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('restaurants', function (Blueprint $table) {
            // add column e_image : string
            $table->string('e_image')->nullable();
            $table->string('p_image')->nullable();
            $table->string('d_image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('restaurants', function (Blueprint $table) {
            $table->dropColumn('e_image');
            $table->dropColumn('p_image');
            $table->dropColumn('d_image');
        });
    }
};
