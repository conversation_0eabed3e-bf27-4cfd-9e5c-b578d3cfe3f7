<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAnnonceReferenceRequest;
use App\Http\Requests\UpdateAnnonceReferenceRequest;
use App\Models\AnnonceReference;

class AnnonceReferenceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAnnonceReferenceRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AnnonceReference $annonceReference)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AnnonceReference $annonceReference)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAnnonceReferenceRequest $request, AnnonceReference $annonceReference)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AnnonceReference $annonceReference)
    {
        //
    }
}
