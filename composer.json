{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "biscolab/laravel-recaptcha": "^6.1", "cinetpay/cinetpay-php": "^1.9", "guzzlehttp/guzzle": "^7.2", "laravel-lang/locales": "^2.7", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.20", "laravel/tinker": "^2.8", "livewire/livewire": "^3.0", "spatie/laravel-cookie-consent": "^3.2", "spatie/laravel-permission": "^5.11", "stevebauman/purify": "^6.0", "wildside/userstamps": "^2.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.9.1", "laravel-lang/common": "^6.2", "laravel-lang/lang": "^15.1", "laravel/pint": "^1.22", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "clear-caches": ["@php artisan cache:clear", "@php artisan config:clear", "@php artisan route:clear", "@php artisan view:clear"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}