<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreOffreAbonnementRequest;
use App\Http\Requests\UpdateOffreAbonnementRequest;
use App\Models\OffreAbonnement;

class OffreAbonnementController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreOffreAbonnementRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OffreAbonnement $offreAbonnement)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OffreAbonnement $offreAbonnement)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateOffreAbonnementRequest $request, OffreAbonnement $offreAbonnement)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OffreAbonnement $offreAbonnement)
    {
        //
    }
}
