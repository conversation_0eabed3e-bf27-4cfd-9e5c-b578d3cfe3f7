<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreHeureOuvertureRequest;
use App\Http\Requests\UpdateHeureOuvertureRequest;
use App\Models\HeureOuverture;

class HeureOuvertureController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreHeureOuvertureRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(HeureOuverture $heureOuverture)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(HeureOuverture $heureOuverture)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateHeureOuvertureRequest $request, HeureOuverture $heureOuverture)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HeureOuverture $heureOuverture)
    {
        //
    }
}
