<div class="col-md-12 col-sm-12">

    <style>
        .tp-author-basic-info {
            margin: 30px 0 0 0;
            padding: 0 25px;
            border-top: 1px solid #ebedf1;
        }

        .tp-author-basic-info ul {
            width: 100%;
            display: table;
        }

        .tp-author-basic-info li {
            list-style: none;
            display: inline-block;
            width: 33.333333%;
            padding: 15px 0 10px;
        }

        .tp-author-basic-info li strong {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #384454;
        }

        .listing-location {
            height: 50px !important;
        }

        .img-responsive {
            object-fit: cover;
            object-position: center;
            width: 100%;
            height: 100%;
        }

        .custom-field-filter {
            margin-top: 6px !important;
            /* margin-bottom: 6px !important;
            height: 40px !important; */
        }
    </style>

    <div class="container">
        <div style="margin-top: 10px;">
            <span id="nbre-favoris" class="mrg-l-10">
                <?php echo e($annonces->firstItem()); ?>-<?php echo e($annonces->lastItem()); ?> sur <?php echo e($annonces->total()); ?>

                annonce(s)
            </span>
        </div>
        <div class="col-12 col-md-4 mb-md-0 mb-2 p-0">
            <select class="form-control custom-field-filter" wire:model.live="type" style="padding-top: 0; padding-bottom: 0;">
                <option value="">Tous les types</option>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($type->valeur); ?>"><?php echo e($type->nom); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </select>
        </div>
        <div class="col-12 col-md-4 mb-md-0 mb-2">
            <select class="form-control custom-field-filter" wire:model.live="is_published" style="padding-top: 0; padding-bottom: 0;">
                <option value="">Toutes les annonces</option>
                <option value="1">Annonces publiées</option>
                <option value="0">Annonces non publiées</option>
            </select>
        </div>
        <div class="col-12 col-md-4 p-0">
            <input id="favorite_search" class="form-control custom-field-filter" type="search" value="" placeholder="Rechercher" wire:model.live.debounce.500ms='search'>
        </div>
    </div>
    <div class="padd-l-0 padd-r-0">
        <div class="col-md-12">
            <div class="small-list-wrapper">
                <div id="table" class="row">
                    <div class="col-md-12 col-sm-12" wire:loading wire:transition>
                        <h4 class="mt-3 text-center">Chargement...</h4>
                    </div>

                    <?php if (isset($component)) { $__componentOriginal1a08db93de59b84f2352bb150f75a8d8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1a08db93de59b84f2352bb150f75a8d8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.public.property-item','data' => ['annonces' => $annonces,'mode' => 'row','showDelete' => 'true','showEdit' => 'true','deleteType' => 'delete']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('public.property-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['annonces' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($annonces),'mode' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('row'),'showDelete' => 'true','showEdit' => 'true','deleteType' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('delete')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1a08db93de59b84f2352bb150f75a8d8)): ?>
<?php $attributes = $__attributesOriginal1a08db93de59b84f2352bb150f75a8d8; ?>
<?php unset($__attributesOriginal1a08db93de59b84f2352bb150f75a8d8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1a08db93de59b84f2352bb150f75a8d8)): ?>
<?php $component = $__componentOriginal1a08db93de59b84f2352bb150f75a8d8; ?>
<?php unset($__componentOriginal1a08db93de59b84f2352bb150f75a8d8); ?>
<?php endif; ?>

                    <!--[if BLOCK]><![endif]--><?php if(empty($annonces->items())): ?>
                        <div class="col-md-12 col-sm-12">
                            <div class="listing-shot grid-style">
                                <div class="listing-shot-caption mrg-top-20 mrg-bot-20 text-center">
                                    <h4>Aucune annonce trouvée</h4>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <?php echo e($annonces->links()); ?>

        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/livewire/admin/annonce.blade.php ENDPATH**/ ?>