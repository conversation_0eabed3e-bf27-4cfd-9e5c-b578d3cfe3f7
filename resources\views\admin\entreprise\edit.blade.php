@extends('layout.admin.app')

@section('entreprise', 'active')

@section('css')
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
@endsection

@section('content')
    <div class="row bg-title" style="padding-top: 20px;">
        <div class="col-lg-6 col-md-10 col-sm-6 col-xs-12">
            <ol class="breadcrumb" style="text-align: left;">
                <li><a href="{{ route('entreprises.index') }}">Entreprise</a></li>
                <li class="active">Modifier un entreprise</li>
            </ol>
        </div>
        <!-- /.col-lg-12 -->
    </div>
    <!-- /. ROW  -->
    <div id="page-inner">
        <div class="row bott-wid">
            <div class="col-md-12 col-sm-12">
                @livewire('admin.entreprise.edit', ['entreprise' => $entreprise])
            </div>
        </div>
    </div>
@endsection

{{-- Use reusable basic map scripts component --}}
<x-admin.basic-map-scripts />

@push('scripts')
<script>
    // Listen for showLocation event
    window.addEventListener('showLocation', event => {
        var lng = event.detail[0].lon;
        var lat = event.detail[0].lat;

        if (mapHelper) {
            mapHelper.setMarker(lat, lng, 12);
            marker = mapHelper.marker;
        }
    });
</script>
@endpush

@endsection
