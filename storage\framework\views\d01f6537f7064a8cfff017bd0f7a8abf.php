<div class="patisserie-template">
    <div>
        <form wire:submit.prevent="store" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
 

            <?php
                $steps = ['Entreprise', 'Location', 'Description', 'Produits', 'Images'];
                $icons = ['fa-briefcase', 'fa-map-marker-alt', 'fa-info-circle', 'fa-birthday-cake', 'fa-image'];
            ?>
            
            <?php if (isset($component)) { $__componentOriginal97a139cad0f6825a497afc64348ab0ae = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal97a139cad0f6825a497afc64348ab0ae = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.form-stepper','data' => ['steps' => $steps,'currentStep' => $currentStep,'icons' => $icons]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.form-stepper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'icons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icons)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $attributes = $__attributesOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $component = $__componentOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__componentOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>

              <!-- Step 1: Company Information -->
            <div class="step-content <?php echo e($currentStep == 0 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.entreprise-template', [
                        'entreprises' => $entreprises,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>

            <!-- Step 2: Location -->
            <div class="step-content <?php echo e($currentStep == 1 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.location-template', [
                        'pays' => $pays,
                        'villes' => $villes,
                        'quartiers' => $quartiers,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="d-flex justify-content-end mt-4">
                    <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
                </div>
            </div>

            <!-- Step 3: Propriété -->
            <div class="step-content <?php echo e($currentStep == 2 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <div class="col-md-12 col-sm-12 p-0">
                        <div class="col">
                            <h3>Description</h3>
                            
                            <textarea id="description" class="form-control" name="description" placeholder="" wire:model.defer='description'></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Équipements',
                        'name' => 'equipements_restauration',
                        'options' => $list_equipements_restauration,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make('admin.annonce.reference-select-component', [
                        'title' => 'Services proposés',
                        'name' => 'services',
                        'options' => $list_services,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>

            <!-- Step 4: Produits -->
            <div class="step-content <?php echo e($currentStep == 3 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <div class="col-md-12 col-sm-12 p-0">
                        <div class="col">
                            <h3>Produits (<?php echo e(count($produits)); ?>)
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>
                            <h4>Carte de produits</h4>
                            <div id="produits-container">
                                <!-- Produit 1 par défaut -->
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $produits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $plat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div id="produit-item-<?php echo e($index + 1); ?>" class="form-group produit-item">
                                        <div>
                                            <button class="btn btn-form" data-bs-toggle="offcanvas" data-bs-target="#produit-<?php echo e($index + 1); ?>" type="button" aria-controls="produit-<?php echo e($index + 1); ?>">
                                                <?php echo e(Str::limit('Produit ' . ($index + 1) . ' : ' . $plat['nom'], 40)); ?> <i class="fa fa-pencil"></i>
                                            </button>
                                        </div>
                                        <div id="produit-<?php echo e($index + 1); ?>" class="offcanvas offcanvas-end" data-bs-scroll="true" aria-labelledby="produit-<?php echo e($index + 1); ?>" tabindex="-1">
                                            <div class="offcanvas-header">
                                                <h5 class="offcanvas-title">Produit <?php echo e($index + 1); ?></h5>
                                                <button id="produits-close-<?php echo e($index + 1); ?>" class="btn-close text-reset" data-bs-dismiss="offcanvas" type="button" aria-label="Close"></button>
                                            </div>
                                            <div class="offcanvas-body">
                                                <div class="form-group">
                                                    <label for="produit-name-<?php echo e($index + 1); ?>">Nom<b style="color: red; font-size: 100%;">*</b></label>
                                                    <input id="produit-name-<?php echo e($index + 1); ?>" class="form-control required-field" type="text" wire:model="produits.<?php echo e($index); ?>.nom">
                                                </div>
                                                <div class="form-group">
                                                    <label for="produit-description-<?php echo e($index + 1); ?>">Accompagnements<b style="color: red; font-size: 100%;">*</b></label>
                                                    <textarea id="produit-description-<?php echo e($index + 1); ?>" class="form-control required-field" wire:model="produits.<?php echo e($index); ?>.accompagnements" rows="3"></textarea>
                                                </div>
                                                <div class="form-group">
                                                    <label for="produit-price-<?php echo e($index + 1); ?>">Prix<b style="color: red; font-size: 100%;">*</b></label>
                                                    <input id="produit-price-<?php echo e($index + 1); ?>" class="form-control required-field" type="number" wire:model="produits.<?php echo e($index); ?>.prix">
                                                </div>
                                                <div class="form-group">
                                                    <label for="form-img-produit-<?php echo e($index + 1); ?>">Image à la Une <b style="color: red; font-size: 100%;">*</b></label>
                                                    <input id="form-img-produit-<?php echo e($index + 1); ?>" class="form-control form-control-file" data-id="<?php echo e($index + 1); ?>" type="file" wire:model="produits.<?php echo e($index); ?>.image" accept="image/*">

                                                    <!--[if BLOCK]><![endif]--><?php if(!empty($produits[$index]['image'])): ?>
                                                        <img class="listing-shot-img img-responsive" src="<?php echo e($produits[$index]['image']->temporaryUrl()); ?>" alt="" style="width: 100%; height: 100px; object-fit: cover;">
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                                </div>
                                                <button class="btn btn-danger delete-produit-btn mb-2" data-produit-id="<?php echo e($index + 1); ?>" type="button" wire:click="removeProduit(<?php echo e($index); ?>)">Supprimer</button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                <div class="col-md-12 col-sm-12 text-center">
                                    <span id="produit-error-message" class="text-danger"><br></span>
                                </div>
                                <!--[if BLOCK]><![endif]--><?php if($produits_error): ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($produits_error); ?></span>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.accompagnements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.prix'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['produits.*.image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="col-md-12 col-sm-12 text-center">
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <button id="add-produit-btn" class="btn btn-success btn-square" type="button"><i class="fa fa-plus"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
            </div>


            <!-- Step 5: Images -->
            <div class="step-content <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>">
                <div class="row align-items-start">
                    <?php echo $__env->make('admin.annonce.create-galery-component', [
                        'galery' => $galerie,
                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>

                <div class="row padd-bot-15 <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>">
                    <div class="form-group">
                        <div class="col text-right">
                            <button id="fast-food-form-submit" class="btn theme-btn" type="submit" style="margin-right: 30px;" wire:loading.attr="disabled">
                                <i class="fa fa-save fa-lg" style="margin-right: 10px;"></i>
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {




        });
    </script>

    <script>
        $('#fast-food-form-submit').on('click', function() {
            //  check if all required fields are filled : I dont want a function

            const produits = collectProduits();

            const lastProduitId = produitCounter - 1;

            function validateAndShowError(type, lastId, items, errorMessageSelector, emptyMessage, invalidMessage) {
                if (lastId > 0 && !validateFields(type, lastId)) {
                    $(errorMessageSelector).text(invalidMessage.replace('{id}', lastId));
                    return false;
                }

                if (items.length === 0) {
                    $(errorMessageSelector).text(emptyMessage);
                    return false;
                }

                return true;
            }

            // Valider les champs du dernier produit avant d'ajouter un nouveau
            if (!validateAndShowError('produit', lastProduitId, produits, '#produit-error-message', 'Veuillez ajouter au moins un produit', 'Veuillez remplir tous les champs obligatoires du produit {id}.')) {
                return false;
            }
        });

        // Add dynamic image upload functionality
        // $(document).on('change', '.form-control-file', function(e) {
        //     var fileInput = $(this);
        //     var file = fileInput[0].files[0];
        //     var reader = new FileReader();
        //     reader.onload = function(e) {
        //         var imgPreview = $('<img>').attr('src', e.target.result).css({
        //             'max-width': '100%',
        //             'height': 'auto',
        //             'margin-top': '10px'
        //         });
        //         fileInput.after(imgPreview);
        //     };
        //     reader.readAsDataURL(file);
        // });
    </script>

    <script type="text/javascript">
        const produitsContainer = $('#produits-container');
        const addProduitBtn = $('#add-produit-btn');
        let produitCounter = 2; // Compteur pour générer des IDs uniques

        // Valider les champs obligatoires et l'unicité du nom
        function validateFields(element, id) {
            let isValid = true;
            const elementName = $(`#${element}-name-${id}`).val();

            // Vérifier si tous les champs obligatoires sont remplis
            $(`#${element}-item-${id} .required-field`).each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid'); // Ajouter une classe pour marquer le champ comme invalide
                } else {
                    $(this).removeClass('is-invalid'); // Enlever la classe si valide
                }
            });

            // Vérifier l'unicité du nom
            if (!isValid) {
                return false; // Si un champ est manquant, on retourne false
            }

            return isValid;
        }

        // Vérifier si le nom du produit est unique
        function isProduitNameUnique(elementName, id) {
            let isUnique = true;
            $('.required-field').each(function() {
                const currentId = $(this).data('${element}-id');
                const currentName = $(`#${elementName}-name-${currentId}`).val();

                // Si le nom est déjà pris et ce n'est pas le même produit
                if (currentName === elementName && currentId !== id) {
                    isUnique = false;
                    return false; // Sortir de la boucle dès qu'un doublon est trouvé
                }
            });
            return isUnique;
        }

        // Ajouter un nouveau produit (avec validation)
        function addProduit() {
            const lastProduitId = produitCounter - 1;

            // Valider les champs du dernier produit avant d'ajouter un nouveau
            if (!validateFields('produit', lastProduitId)) {
                $('#produit-error-message').text(`Veuillez remplir tous les champs obligatoires du produit ${lastProduitId}.`);
                return; // Stopper l'ajout si les champs ne sont pas valides
            }

            window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('addProduit');

            produitCounter++;

            // click sur le bouton pour ouvrir le formulaire (canvas)
            $(`#produit-${produitCounter - 1}`).offcanvas('show');
        }

        // Réordonner les produits après suppression
        function reorderProduits() {
            produitsContainer.children('.produit-item').each(function(index) {
                const newIndex = index + 1; // Nouvel index (commence à 1)
                const produitItem = $(this);

                // Mettre à jour le conteneur principal
                produitItem.attr('id', `produit-item-${newIndex}`);

                // Mettre à jour le bouton
                const button = produitItem.find('.btn-form');
                button.text(`Produit ${newIndex}`);
                button.attr('data-bs-target', `#produit-${newIndex}`);
                button.attr('aria-controls', `produit-${newIndex}`);

                // Mettre à jour la zone offcanvas
                const offcanvas = produitItem.find('.offcanvas');
                offcanvas.attr('id', `produit-${newIndex}`);
                offcanvas.attr('aria-labelledby', `produit-${newIndex}`);
                offcanvas.find('.offcanvas-title').text(`Produit ${newIndex}`);

                // Mettre à jour les champs dans le formulaire
                produitItem.find('label[for^="name"]').attr('for', `name-${newIndex}`);
                produitItem.find('label[for^="description"]').attr('for', `description-${newIndex}`);
                produitItem.find('label[for^="price"]').attr('for', `price-${newIndex}`);
                produitItem.find('label[for^="form-img"]').attr('for', `form-img-${newIndex}`);
                produitItem.find('input[id^="name"]').attr('id', `name-${newIndex}`);
                produitItem.find('textarea[id^="description"]').attr('id', `description-${newIndex}`);
                produitItem.find('input[id^="price"]').attr('id', `price-${newIndex}`);
                produitItem.find('input[id^="form-img"]').attr('id', `form-img-${newIndex}`);

                // Mettre à jour les boutons
                produitItem.find('.save-produit-btn').attr('data-produit-id', newIndex);
                produitItem.find('.delete-produit-btn').attr('data-produit-id', newIndex);
            });

            // Réinitialiser le compteur au dernier index + 1
            produitCounter = produitsContainer.children('.produit-item').length + 1;
        }

        // Supprimer un produit
        $(document).on('click', '.delete-produit-btn', function() {
            const produitId = $(this).data('produit-id');
            $('#produit-error-message').text(''); // Réinitialiser le message d'erreur
            $(`#produit-item-${produitId}`).remove();
            reorderProduits(); // Réordonner après suppression
        });

        // Enregistrer un produit (vous pouvez personnaliser selon vos besoins)
        $(document).on('click', '.save-produit-btn', function() {
            const produitId = $(this).data('produit-id');
            if (validateFields('produit', produitId)) {
                const produitName = $(`#produit-name-${produitId}`).val();
                const produitaccompagnements = $(`#produit-description-${produitId}`).val();
                const produitPrice = $(`#produit-price-${produitId}`).val();

                // Fermer le offcanvas après enregistrement
                $(`#produit-${produitId}`).offcanvas('hide');
                $('#produit-error-message').text(''); // Réinitialiser le message d'erreur
            }
        });

        // Collecter les produits et envoyer
        function collectProduits() {
            let produitsData = [];

            $('.produit-item').each(function() {
                const produitId = $(this).attr('id').split('-')[2]; // Extraire l'ID du produit
                const name = $(`#produit-name-${produitId}`).val();
                const description = $(`#produit-description-${produitId}`).val();
                const price = $(`#produit-price-${produitId}`).val();
                const image = $(`#form-img-produit-${produitId}`).val(); // Si une image est incluse, vous pourrez la gérer ici

                produitsData.push({
                    nom: name,
                    accompagnements: description,
                    prix: price,
                    image: image // Si image est définie, elle sera envoyée
                });
            });

            return clearData(produitsData);
        }

        const clearData = (objets) => {
            return objets.filter(objet => {
                return Object.values(objet).some(value => value !== null && value !== '');
            });
        }

        // Événement pour le bouton "Ajouter"
        addProduitBtn.on('click', addProduit);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/livewire/admin/patisserie/create.blade.php ENDPATH**/ ?>