<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['steps', 'currentStep', 'icons' => null]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['steps', 'currentStep', 'icons' => null]); ?>
<?php foreach (array_filter((['steps', 'currentStep', 'icons' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="stepper-wrapper mb-5">
    <div class="stepper-progress d-none d-md-flex">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="stepper-step">
                <button type="button" 
                    class="stepper-circle <?php echo e($currentStep > $index ? 'completed' : ($currentStep == $index ? 'active' : '')); ?>"
                    wire:click="$set('currentStep', <?php echo e($index); ?>)"
                    <?php echo e($currentStep < $index ? 'disabled' : ''); ?>>
                    <!--[if BLOCK]><![endif]--><?php if($icons): ?>
                        <i class="fa <?php echo e($icons[$index]); ?>"></i>
                    <?php else: ?>
                        <?php echo e($index + 1); ?>

                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </button>
                <div class="stepper-label <?php echo e($currentStep >= $index ? 'active' : ''); ?>"><?php echo e($step); ?></div>
                <!--[if BLOCK]><![endif]--><?php if($index < count($steps) - 1): ?>
                    <div class="stepper-line <?php echo e($currentStep > $index ? 'completed' : ''); ?>"></div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
    
    <!-- Mobile stepper -->
    <div class="d-md-none mx-2">
        <div class="d-flex justify-content-between align-items-center">
            <span class="font-weight-medium">
                Étape <?php echo e($currentStep + 1); ?> sur <?php echo e(count($steps)); ?>: 
                <?php echo e($steps[$currentStep]); ?>

            </span>
            <span class="badge badge-primary"><?php echo e($currentStep + 1); ?>/<?php echo e(count($steps)); ?></span>
        </div>
        <div class="progress mt-2">
            <div class="progress-bar bg-vamiyi-orange" role="progressbar" 
                style="width: <?php echo e(($currentStep + 1) * (100/count($steps))); ?>%" 
                aria-valuenow="<?php echo e(($currentStep + 1) * (100/count($steps))); ?>" 
                aria-valuemin="0" 
                aria-valuemax="100">
            </div>
        </div>
        
        
        <div class="d-flex justify-content-between align-items-center mt-3 gap-2">
            <button type="button" 
                class="btn d-flex align-items-center justify-content-center"
                wire:click="$set('currentStep', <?php echo e($currentStep - 1); ?>)"
                <?php echo e($currentStep == 0 ? 'disabled' : ''); ?>

                style="flex: 1;">
                <i class="fa fa-chevron-left me-2"></i>
                <span>Précédent</span>
            </button>
            

            <!--[if BLOCK]><![endif]--><?php if($currentStep < count($steps)): ?>
             <button id="submit-btn" class="btn theme-btn" type="button" wire:click="nextStep" wire:loading.attr='disabled'>
                 <span>Suivant</span>
                <i class="fa fa-chevron-right ms-2"></i>
            </button>
            </button>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/components/admin/form-stepper.blade.php ENDPATH**/ ?>