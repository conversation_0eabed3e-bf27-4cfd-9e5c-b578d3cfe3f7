<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'mapId' => 'map',
    'latitude' => null,
    'longitude' => null,
    'editable' => true,  
    'zoom' => 13,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'mapId' => 'map',
    'latitude' => null,
    'longitude' => null,
    'editable' => true,  
    'zoom' => 13,
]); ?>
<?php foreach (array_filter(([
    'mapId' => 'map',
    'latitude' => null,
    'longitude' => null,
    'editable' => true,  
    'zoom' => 13,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function () {
    let map, marker;

    function initMap() {
        // Default center if no coordinates
        let center = {
            lat: <?php echo e($latitude ?? 8.6195); ?>,
            lng: <?php echo e($longitude ?? 0.8248); ?>

        };

        // Initialize Google Map
        map = new google.maps.Map(document.getElementById('<?php echo e($mapId); ?>'), {
            zoom: <?php echo e($zoom); ?>,
            center: center
        });

        // If we have coordinates, place marker
        if (<?php echo e($latitude ? 'true' : 'false'); ?> && <?php echo e($longitude ? 'true' : 'false'); ?>) {
            marker = new google.maps.Marker({
                position: center,
                map: map
            });
        }

        <!--[if BLOCK]><![endif]--><?php if($editable): ?>
        // Allow map click to set marker
        map.addListener("click", function(e) {
            placeMarker(e.latLng);
        });

        // "Me localiser" button
        document.querySelector('#locateBtn')?.addEventListener('click', function() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    let pos = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    map.setCenter(pos);
                    placeMarker(pos);
                });
            }
        });
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    }

    // Place or move marker + update Livewire model
    function placeMarker(location) {
        if (marker) {
            marker.setPosition(location);
        } else {
            marker = new google.maps.Marker({
                position: location,
                map: map
            });
        }

        // Update Livewire fields
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('latitude', location.lat);
        window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('longitude', location.lng);
    }

    // Initialize when Google script loads
    if (typeof google !== 'undefined') {
        initMap();
    } else {
        console.error("Google Maps API not loaded");
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/components/public/maps-scripts.blade.php ENDPATH**/ ?>