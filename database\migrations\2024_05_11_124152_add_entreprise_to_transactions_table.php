<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('entreprise')->nullable();
            $table->string('numero')->nullable();
            $table->string('numero_whatsapp')->nullable();
            $table->integer('ville_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn('entreprise');
            $table->dropColumn('numero');
            $table->dropColumn('numero_whatsapp');
        });
    }
};
