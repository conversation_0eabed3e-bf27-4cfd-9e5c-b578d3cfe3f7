-- MySQL dump 10.13  Distrib 8.0.38, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: annonce4
-- ------------------------------------------------------
-- Server version	8.0.38

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `reference_valeurs`
--

DROP TABLE IF EXISTS `reference_valeurs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reference_valeurs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `valeur` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `reference_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `deleted_by` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reference_valeurs_reference_id_foreign` (`reference_id`),
  CONSTRAINT `reference_valeurs_reference_id_foreign` FOREIGN KEY (`reference_id`) REFERENCES `references` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reference_valeurs`
--

/*!40000 ALTER TABLE `reference_valeurs` DISABLE KEYS */;
INSERT INTO `reference_valeurs` VALUES (1,'Piscine',1,'2024-11-21 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(2,'Salle de sport',1,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(3,'Balcon/terrasse',1,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(4,'Jardin ou barbecue',1,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(5,'Jeux et divertissements',1,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(6,'Nettoyage d\'entrée/sortie',2,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(7,'Conciergerie',2,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(8,'Gardiennage',2,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(9,'Petit déjeuner compris',2,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(10,'Lit simple',3,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(11,'Lit double',3,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(12,'Queen size',3,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(13,'Lit bébé',3,'2024-11-22 08:59:53','2024-12-11 09:06:38','2024-12-11 09:06:38',1,1,1),(14,'Lits superposés',3,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(15,'Placards, commodes, armoires',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(16,'Système de sécurité (caméras, alarmes)',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(17,'Connexion internet',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(18,'Détecteurs de fumée et de monoxyde de carbone',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(19,'Aspirateur ',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(20,'Multiprises et chargeurs',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(21,'Canapé',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(22,'Lave-linge.',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(23,'Sèche-linge (ou étendoir)',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(24,'Table et fer à repasser',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(25,'Kit de premiers secours',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(26,'Jeux ou jouets',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(27,'Climatisée',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(28,'Ventilée',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(29,'Téléviseur',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(30,'Imprimante',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(31,'Ordinateur',4,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(32,'Lavabo ou Vasque',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(33,'Cabine de douche',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(34,'Baignoire',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(35,'WC intégré salle de bain',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(36,'WC séparé',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(37,'Panier à linge',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(38,'Armoire de toilette',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(39,'Colonnes de rangement',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(40,'Eau chaude',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(41,'Tapis de bain',5,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(42,'Four',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(43,'Plaques de cuissons',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(44,'Plan de travail',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(45,'Réfrigérateur',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(46,'Lave-vaisselle',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(47,'Hotte aspirante',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(48,'Poubelle intégrée',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(49,'Micro-ondes',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(50,'Bouilloire',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(51,'Blendeur/mixeur',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(52,'Machine à café',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(53,'Grille-pain',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(54,'Friteuse',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(55,'Verres à vin',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(56,'Couverts',6,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(57,'Maison T4 (3 chambres salon)',7,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(58,'Maison T3 (2 chambres salon)',7,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(59,'Maison T2 (chambre salon)',7,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(60,'T1 ou Studio',7,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(61,'Appartement T4 (3 chambres)',7,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(62,'Appartement T3 (2 chambres)',7,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(63,'Appartement T2 (Chambre salon)',7,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(64,'Utilitaire ( Camion )',8,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(65,'Citadine',8,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(66,'Berline',8,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(67,'Familiale ( 7 places ) ',8,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(68,'4x4',8,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(69,'SUV',8,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(70,'Minibus',8,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(71,'Climatisation',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(72,'Sièges réglables ',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(73,'Accoudoir central',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(74,'Pare-soleil avec miroir éclairé',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(75,'Airbags (frontaux, latéraux, rideaux)',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(76,'Caméra de recul',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(77,'Freinage automatique d’urgence',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(78,'Navigation GPS',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(79,'Commandes vocales',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(80,'Connectivité Bluetooth et USB',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(81,'Apple CarPlay / Android Auto',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(82,'Pare-soleil intégrés',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(83,'Régulateur de vitesse',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(84,'Limiteur de vitesse',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(85,'Caméra 360°',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(86,'Toit ouvrant ou panoramique',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(87,'Feux antibrouillard',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(88,'Siège bébé ou fixation ISOFIX',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(89,'Espace de rangement sous le coffre',9,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(90,'Minimum 3 jours',12,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(91,'18 ans minimum',12,'2024-11-22 08:59:53','2024-12-02 15:39:02',NULL,1,1,NULL),(92,'Ancienneté de 2 ans de permis',12,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(93,'Ancienneté de plus de 3 ans de permis',12,'2024-11-22 08:59:53','2024-12-02 15:33:34',NULL,1,1,NULL),(94,'Ne doit pas traverser une frontière',12,'2024-11-22 08:59:53','2024-11-22 08:59:53',NULL,1,1,NULL),(95,'Spe1',22,'2024-11-22 09:57:27','2024-11-22 13:48:59','2024-11-22 13:48:59',1,1,1),(96,'cwe',6,'2024-11-22 13:26:54','2024-11-22 13:48:52','2024-11-22 13:48:52',1,1,1),(97,'fgbgbgb',6,'2024-11-22 13:52:59','2024-11-22 13:53:51','2024-11-22 13:53:51',1,1,1),(98,'frfefefre',1,'2024-11-22 14:01:10','2024-11-22 14:01:48','2024-11-22 14:01:48',1,1,1),(99,'cwecwcece',6,'2024-11-22 14:05:02','2024-11-22 14:21:01','2024-11-22 14:21:01',1,1,1),(100,'cwececwe',20,'2024-11-22 14:05:09','2024-11-22 14:20:53','2024-11-22 14:20:53',1,1,1),(101,'ecwece',4,'2024-11-22 14:05:51','2024-11-22 14:20:48','2024-11-22 14:20:48',1,1,1),(102,'dwdwedew',6,'2024-11-22 14:06:03','2024-11-22 14:20:45','2024-11-22 14:20:45',1,1,1),(103,'eerfrer',1,'2024-11-22 14:11:51','2024-11-22 14:20:41','2024-11-22 14:20:41',1,1,1),(104,'cdcdcfc',6,'2024-11-22 14:12:27','2024-11-22 14:20:37','2024-11-22 14:20:37',1,1,1),(105,'efrferfer',12,'2024-11-22 14:12:48','2024-11-22 14:20:31','2024-11-22 14:20:31',1,1,1),(106,'scdscsd',1,'2024-11-22 14:18:08','2024-11-22 14:19:04','2024-11-22 14:19:04',1,1,1),(107,'Commodité test 2',1,'2024-11-23 19:39:48','2024-11-23 19:40:08','2024-11-23 19:40:08',1,1,1),(108,'TOYOTA',13,'2024-11-23 21:03:54','2024-11-23 21:03:54',NULL,1,1,NULL),(109,'Ras',6,'2024-11-23 21:05:59','2024-12-02 13:51:28','2024-12-02 13:51:28',1,1,1),(110,'dwede',1,'2024-11-23 21:08:28','2024-12-02 13:51:23','2024-12-02 13:51:23',1,1,1),(111,'dwde',1,'2024-11-23 21:09:50','2024-12-02 13:51:19','2024-12-02 13:51:19',1,1,1),(112,'dewedw',12,'2024-11-23 21:10:53','2024-12-02 13:51:13','2024-12-02 13:51:13',1,1,1),(113,'OPEL',13,'2024-12-19 15:27:26','2024-12-19 15:27:26',NULL,1,1,NULL),(114,'AUDI',13,'2024-12-19 15:27:40','2024-12-19 15:27:40',NULL,1,1,NULL),(115,'MAZDA',13,'2024-12-19 15:29:01','2024-12-19 15:29:01',NULL,1,1,NULL),(116,' ',13,'2024-12-19 15:29:01','2024-12-19 15:31:50','2024-12-19 15:31:50',1,1,1),(117,'BMW',13,'2024-12-19 15:29:01','2024-12-19 15:29:01',NULL,1,1,NULL),(118,'ABe',13,'2024-12-19 15:31:41','2024-12-23 10:10:52','2024-12-23 10:10:52',1,1,1),(119,'QS',13,'2024-12-19 15:31:41','2024-12-19 15:39:32','2024-12-19 15:39:32',1,1,1),(120,'QSR,bf,bf',13,'2024-12-19 15:31:41','2024-12-19 15:39:24','2024-12-19 15:39:24',1,1,1),(121,'TEST 12378787',13,'2024-12-19 15:36:23','2024-12-19 15:39:19','2024-12-19 15:39:19',1,1,1),(122,'QSR',13,'2024-12-19 15:36:44','2024-12-19 15:39:28','2024-12-19 15:39:28',1,1,1),(123,'TEST 123',13,'2024-12-19 15:36:58','2024-12-19 15:39:05','2024-12-19 15:39:05',1,1,1),(124,'TESTA 1237',13,'2024-12-19 15:37:04','2024-12-19 15:39:01','2024-12-19 15:39:01',1,1,1),(125,'Dîner sur place',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(126,'à emporter',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(127,'livraison',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(128,'Wifi disponible',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(129,'Réservation obligatoire',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(130,'Parking',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(131,'organisation d\'événement',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(132,'payement par carte bancaire',24,'2024-12-23 10:07:57','2024-12-23 10:07:57',NULL,1,1,NULL),(133,'Test 123',20,'2024-12-23 15:03:07','2024-12-23 15:04:23','2024-12-23 15:04:23',1,1,1),(134,'Eq vie noc',21,'2024-12-23 15:03:35','2024-12-23 15:04:19','2024-12-23 15:04:19',1,1,1),(135,'test',12,'2024-12-26 14:41:51','2024-12-26 14:41:57','2024-12-26 14:41:57',1,1,1),(136,'Manuelle',11,'2024-12-26 15:45:42','2024-12-26 15:45:42',NULL,1,1,NULL),(137,'type1',8,'2024-12-26 15:47:14','2024-12-26 15:47:14',NULL,1,1,NULL),(138,'type2',8,'2024-12-26 15:47:49','2024-12-26 15:47:49',NULL,1,1,NULL),(139,'type3',8,'2024-12-26 15:47:49','2024-12-26 15:47:49',NULL,1,1,NULL),(140,'moteur1',18,'2024-12-26 15:55:28','2024-12-26 15:55:28',NULL,1,1,NULL);
/*!40000 ALTER TABLE `reference_valeurs` ENABLE KEYS */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-12-30 21:15:51
