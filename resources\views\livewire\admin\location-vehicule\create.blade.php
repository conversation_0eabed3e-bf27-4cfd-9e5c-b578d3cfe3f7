<div>
    <form wire:submit.prevent="store">
        @csrf

        <div class="card-header bg-vamiyi-orange py-4 text-center text-white">
            <h2 class="card-title font-weight-bold">Remplissez les informations</h2>
        </div>
        <div class="card-body pt-4">
                @php
                    $steps = ['Entreprise', 'Location', 'Véhicule', 'Spécifications', 'Images'];
                    $icons = ['fa-building', 'fa-map-marker-alt', 'fa-car', 'fa-cogs', 'fa-images'];
                @endphp
            <!-- Stepper -->
            <x-admin.form-stepper :steps="$steps" :currentStep="$currentStep" :icons="$icons" />

            <!-- Step 1: Company Information -->
            <div class="step-content {{ $currentStep == 0 ? '' : 'd-none' }}">
                <div class="row align-items-start">
                    @include('admin.annonce.entreprise-template', [
                        'entreprises' => $entreprises,
                    ])
                </div>
                <x-admin.step-navigation :currentStep="$currentStep" :lastStep="4" />
            </div>

            <!-- Step 2: Location -->
            <div class="step-content {{ $currentStep == 1 ? '' : 'd-none' }}">
                <div class="row align-items-start">
                    @include('admin.annonce.location-template', [
                        'pays' => $pays,
                        'villes' => $villes,
                        'quartiers' => $quartiers,
                    ])
                </div>
                <div class="d-flex justify-content-end mt-4">
                    <button type="button" class="btn theme-btn" wire:click="nextStep">
                        Continuer
                    </button>
                </div>
            </div>


            <!-- Step 2: Vehicle Details -->
            <div class="step-content {{ $currentStep == 2 ? '' : 'd-none' }} m-0">
                <div class="row align-items-start">
                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Marque
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select id="marque" class="form-control" data-nom="marque_id" wire:model.lazy='marque_id' required>
                                <option value="">-- Sélectionner --</option>
                                @foreach ($list_marques as $marque)
                                    <option value="{{ $marque->id }}">{{ $marque->nom }}</option>
                                @endforeach
                            </select>
                            @error('marque')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Modèle
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select class="form-control" data-nom="modele_id" wire:model.lazy='modele_id' required>
                                <option value="">-- Sélectionner --</option>
                                @foreach ($list_modeles as $modele)
                                    <option value="{{ $modele->id }}">{{ $modele->nom }}</option>
                                @endforeach
                            </select>
                            @error('modele_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Année</h3>

                            <input class="form-control" type="number" placeholder="" wire:model.defer='annee'>
                            @error('annee')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Kilométrage</h3>

                            <input class="form-control montant-format" type="text" placeholder="" wire:model.defer='kilometrage'>
                            @error('kilometrage')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Nombre de places
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <input class="form-control" type="number" placeholder="" wire:model.defer='nombre_places'>
                            @error('nombre_places')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Nombre de portes
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select class="form-control" data-nom="nombre_portes" wire:model.lazy='nombre_portes' required>
                                <option value="">-- Sélectionner --</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                            @error('nombre_portes')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    @include('admin.annonce.price-component', [
                        'min' => true,
                        'required' => true,
                        'withTitle' => false,
                    ])

                    @include('admin.annonce.price-component', [
                        'min' => false,
                        'withTitle' => false,
                    ])

                </div>
                <div class="d-flex justify-content-between my-4">
                    <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                        Retour
                    </button>
                    <button type="button" class="btn theme-btn" wire:click="nextStep">
                        Continuer
                    </button>

                </div>

            </div>

            <!-- Step 3: Specifications -->
            <div class="step-content {{ $currentStep == 3 ? '' : 'd-none' }}">
                <div class="row align-items-start">
                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Type de moteur
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select id="carburant" class="form-control" data-nom="carburant" wire:model.defer='carburant' required>
                                <option value="">-- Sélectionner --</option>
                                @foreach ($list_types_carburant as $item)
                                    <option value="{{ $item->valeur }}">{{ $item->valeur }}</option>
                                @endforeach
                            </select>
                            @error('carburant')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-12 p-0">
                        <div class="col">
                            <h3>Boite de vitesses
                                <b style="color: red; font-size: 100%;">*</b>
                            </h3>

                            <select id="boite_vitesses" class="form-control" data-nom="boite_vitesses" wire:model.defer='boite_vitesses' required>
                                <option value="">-- Sélectionner --</option>
                                @foreach ($list_boites_vitesse as $item)
                                    <option value="{{ $item->valeur }}">{{ $item->valeur }}</option>
                                @endforeach
                            </select>
                            @error('boite_vitesses')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row align-items-start">
                    @include('admin.annonce.description-component')
                </div>

                <div class="row align-items-start">

                    @include('admin.annonce.reference-select-component', [
                        'title' => 'Type de voiture',
                        'name' => 'types_vehicule',
                        'options' => $list_types_vehicule,
                        'required' => true,
                    ])

                    @include('admin.annonce.reference-select-component', [
                        'title' => 'Options et accessoires',
                        'name' => 'equipements_vehicule',
                        'options' => $list_equipements_vehicule,
                    ])

                    @include('admin.annonce.reference-select-component', [
                        'title' => 'Conditions de location',
                        'name' => 'conditions_location',
                        'options' => $list_conditions_location,
                    ])

                </div>

                <!-- <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                            Retour
                        </button>
                        <button type="button" class="btn theme-btn" wire:click="nextStep">
                            Continuer
                        </button>
                    </div> -->
                <div class="d-flex justify-content-between my-4" style="height: 100%;">
                    <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                        Retour
                    </button>
                    <button type="button" class="btn theme-btn" wire:click="nextStep">
                        Continuer
                    </button>
                </div>
            </div>

            <!-- Step 4: Gallery (don't ask why step 4 before 2) (and dont move it) -->
            <div class="step-content {{ $currentStep == 4 ? '' : 'd-none' }}">
                <div class="row">
                    @include('admin.annonce.create-galery-component', [
                        'galery' => $galerie,
                    ])
                </div>
                <div class="d-flex justify-content-between {{ $currentStep == 4 ? '' : 'd-none' }} my-4">
                    <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                        Retour
                    </button>
                    <button id="submit-btn" class="btn theme-btn" type="submit" wire:loading.attr='disabled'>
                        <i class="fa fa-save fa-lg"></i>
                        Enregistrer
                    </button>
                </div>
            </div>
        </div>

    </form>

</div>
