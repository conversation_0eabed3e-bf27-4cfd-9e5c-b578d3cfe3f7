<?php $__env->startSection('title', 'Mes annonces'); ?>

<?php $__env->startSection('content'); ?>

    <?php
        $breadcrumbs = [['route' => 'accueil', 'label' => 'Accueil'], ['label' => 'Annonces']];
    ?>

    <?php if (isset($component)) { $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcumb','data' => ['backgroundImage' => ''.e(asset('assets_client/img/banner/image-2.jpg')).'','showTitle' => true,'title' => 'Mes annonces','showSearchButton' => true,'breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('breadcumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['backgroundImage' => ''.e(asset('assets_client/img/banner/image-2.jpg')).'','showTitle' => true,'title' => 'Mes annonces','showSearchButton' => true,'breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $attributes = $__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__attributesOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd)): ?>
<?php $component = $__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd; ?>
<?php unset($__componentOriginal7b7e0a1d64d91ec9f766bb4615d302bd); ?>
<?php endif; ?>

    <div class="page-name auberge row">
        <div class="container text-left p-0 mt-4 mb-4">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('admin.annonce');

$__html = app('livewire')->mount($__name, $__params, 'lw-1866399741-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.public.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/public/user/annonce/index.blade.php ENDPATH**/ ?>