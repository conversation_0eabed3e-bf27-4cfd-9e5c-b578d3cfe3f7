/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #af002a !important;
}
.heading h2 span {
    color: #af002a;
}
.theme-cl {
    color: #af002a;
}
.theme-bg {
    background: #af002a;
}
span.like-listing i {
    background: #af002a;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background: #af002a;
}
.feature-box span {
    background: #af002a;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#af002a;
    border-color:#af002a;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background: #af002a;
    border-color: #af002a;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background: #af002a;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#af002a;
	border-color:#af002a;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background: #af002a;
}
span.category-tag {
    color: #af002a;
    border: 1px solid #af002a;
}
/*---prices---*/
.active .package-header {
    background: #af002a;
}
button.btn.btn-package {
    background: #af002a;
}
/*----button colors---*/
.theme-btn {
    background: #af002a;
    border: 1px solid #af002a;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background: #af002a;
    border: 1px solid #af002a;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #af002a;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#af002a;
	border-color:#af002a;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(175, 0, 42,0.1);
    color: #af002a;
    border-radius: 50px;
    border: 1px solid #af002a;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(175, 0, 42,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #af002a;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#af002a;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#af002a;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#af002a;
	border:1px solid #af002a;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#af002a;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#af002a;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#af002a;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#af002a;
}
a.btn.listing-btn {
    background: #af002a;
    border: 1px solid #af002a;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color: #af002a;
}
.title-content a{
	color:#af002a;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color: #af002a;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background: #af002a;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background: #af002a;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background: #af002a;
    border: 1px solid #af002a;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color: #af002a;
    background-color: rgba(175, 0, 42,0.12);
    border-color: #af002a;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background: #af002a;
    border: 1px solid transparent;
}
.layout-option a.active {
    color: #af002a;
}
.layout-option a:hover, .layout-option a:focus {
    color: #af002a;
}
.edit-info .btn {
    border: 1px solid #af002a;
    color: #af002a;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#af002a;
	background:#af002a;
}
ul.social-info.info-list li i {
    color: #af002a;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color: #af002a;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color: #af002a;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #af002a;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #af002a;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color: #af002a;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color: #af002a;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background: #af002a;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color: #af002a;
    border-bottom: 2px solid #af002a;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color: #af002a;
    border-bottom: 2px solid #af002a;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #af002a;
    background: #af002a;
}

.footer-copyright p a {
    color: #af002a;
}
.footer-social li a:hover i {
    background: #af002a;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #af002a;
    border: 1px solid #af002a;
}
.small-list-detail p a, p a {
    color: #af002a;
}
.quote-card::before {
    color:#af002a;
}
.quote-card cite {
    color:#af002a;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #af002a;
}
.service-box:before {
    border-left: 1px solid #af002a;
    border-right: 1px solid #af002a;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #af002a;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #af002a;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #af002a;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#af002a;
}
.service-box:after {
    border-bottom: 1px solid #af002a;
    border-top: 1px solid #af002a;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#af002a;
}
.range-slider .slider-selection {
    background:#af002a;
}
.range-slider .slider-handle.round {
    border: 2px solid #af002a;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #af002a;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background: #af002a !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background: #af002a !important;
}
}