<div>
    <form wire:submit.prevent="store">
        <?php echo csrf_field(); ?>

            <div class="card-header bg-vamiyi-orange text-white text-center py-4">
                <h2 class="card-title font-weight-bold">Remplissez les informations</h2>
            </div>
            <div class="card-body pt-4">
                <?php
                    $steps = ['Entreprise', 'Location', 'Propriété', 'Caractéristiques', 'Images'];
                    $icons = ['fa-building', 'fa-map-marker-alt', 'fa-home', 'fa-list', 'fa-images'];
                ?>
                
                <?php if (isset($component)) { $__componentOriginal97a139cad0f6825a497afc64348ab0ae = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal97a139cad0f6825a497afc64348ab0ae = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.form-stepper','data' => ['steps' => $steps,'currentStep' => $currentStep,'icons' => $icons]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.form-stepper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'icons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icons)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $attributes = $__attributesOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__attributesOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97a139cad0f6825a497afc64348ab0ae)): ?>
<?php $component = $__componentOriginal97a139cad0f6825a497afc64348ab0ae; ?>
<?php unset($__componentOriginal97a139cad0f6825a497afc64348ab0ae); ?>
<?php endif; ?>

                <!-- Step 1: Entreprise -->
                <div class="step-content <?php echo e($currentStep == 0 ? '' : 'd-none'); ?>">
                        <div class="row align-items-start">
                            <?php echo $__env->make('admin.annonce.entreprise-template', [
                                'entreprises' => $entreprises,
                            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    
                    <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
                </div>

                <!-- Step 2: Location -->
                <div class="step-content <?php echo e($currentStep == 1 ? '' : 'd-none'); ?>">
                        <div class="row align-items-start">
                            <?php echo $__env->make('admin.annonce.location-template', [
                                'pays' => $pays,
                                'villes' => $villes,
                                'quartiers' => $quartiers,
                                'longitude' => $longitude,
                                'latitude' => $latitude,
                            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    
                    <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
                </div>

                <!-- Step 3: Propriété -->
                <div class="step-content <?php echo e($currentStep == 2 ? '' : 'd-none'); ?>">
                    <div class="row align-items-start">
                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3 class="required">Nombre de chambre</h3>
                                <h4>Indiquez le nombre de chambre</h4>
                                <input class="form-control" name="nombre_chambre" type="number" placeholder="" wire:model.defer='nombre_chambre' required>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['nombre_chambre'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3>Nombre de personnes</h3>
                                <h4>Indiquez le nombre de personnes</h4>
                                <input class="form-control" name="nombre_personne" type="number" placeholder="" wire:model.defer='nombre_personne'>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['nombre_personne'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3>Nombre de salle de bain</h3>
                                <h4>Indiquez le nombre de salle de bain</h4>
                                <input class="form-control" name="nombre_salles_bain" type="number" placeholder="" wire:model.defer='nombre_salles_bain'>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['nombre_salles_bain'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>

                    <div class="row align-items-start mt-3">
                        <div class="col-md-4 col-xs-12 p-0">
                            <div class="col">
                                <h3>Superficie</h3>
                                <h4>Indiquez la superficie en m²</h4>
                                <input class="form-control" name="superficie" type="number" placeholder="" wire:model.defer='superficie'>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['superficie'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 min-price p-0">
                            <div class="col">
                                <h3>Prix minimum</h3>
                                <h4>Indiquez le prix minimum</h4>
                                <input class="form-control" name="prix_min" type="number" placeholder="" wire:model='prix_min'>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['prix_min'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <div class="col-md-4 col-xs-12 max-price p-0">
                            <div class="col">
                                <h3>Prix maximum</h3>
                                <h4>Indiquez le prix maximum</h4>
                                <input class="form-control" name="prix_max" type="number" placeholder="" wire:model='prix_max'>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['prix_max'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>

                    <div class="row align-items-start mt-3">
                        <div class="col-12">
                            <h3>Description</h3>
                            <textarea class="form-control" wire:model.defer="description" rows="5"></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    
                    <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
                </div>

                <!-- Step 4: Caractéristiques -->
                <div class="step-content <?php echo e($currentStep == 3 ? '' : 'd-none'); ?>">
                    <div class="row align-items-start">
                        <?php echo $__env->make('admin.annonce.reference-select-component', [
                            'title' => 'Type d\'hebergement',
                            'name' => 'types_hebergement',
                            'options' => $list_types_hebergement,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <?php echo $__env->make('admin.annonce.reference-select-component', [
                            'title' => 'Type de lit',
                            'name' => 'types_lit',
                            'options' => $list_types_lit,
                            'required' => true,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <?php echo $__env->make('admin.annonce.reference-select-component', [
                            'title' => 'Commodités',
                            'name' => 'commodites',
                            'options' => $list_commodites,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <div class="row align-items-start mt-3">
                        <?php echo $__env->make('admin.annonce.reference-select-component', [
                            'title' => 'Services proposés',
                            'name' => 'services',
                            'options' => $list_services,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <?php echo $__env->make('admin.annonce.reference-select-component', [
                            'title' => 'Equipements d\'hébergement',
                            'name' => 'equipements_herbegement',
                            'options' => $list_equipements_herbegement,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        <?php echo $__env->make('admin.annonce.reference-select-component', [
                            'title' => 'Equipements de cuisine',
                            'name' => 'equipements_cuisine',
                            'options' => $list_equipements_cuisine,
                            'required' => true,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <div class="row align-items-start mt-3">
                        <?php echo $__env->make('admin.annonce.reference-select-component', [
                            'title' => 'Equipements de salle de bain',
                            'name' => 'equipements_salle_bain',
                            'options' => $list_equipements_salle_bain,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    
                    <?php if (isset($component)) { $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.step-navigation','data' => ['currentStep' => $currentStep,'lastStep' => 4]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.step-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'lastStep' => 4]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $attributes = $__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__attributesOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec)): ?>
<?php $component = $__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec; ?>
<?php unset($__componentOriginalf631cf1f87c5baa1a70e006ef91e8cec); ?>
<?php endif; ?>
                </div>

                <!-- Step 5: Images -->
                <div class="step-content <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>"> 
                    <div class="row">
                        <?php echo $__env->make('admin.annonce.create-galery-component', [
                            'galery' => $galerie,
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <div class="d-flex justify-content-between my-4 <?php echo e($currentStep == 4 ? '' : 'd-none'); ?>">
                        <button type="button" class="btn btn-outline-secondary" wire:click="previousStep">
                            Retour
                        </button>
                        <button id="submit-btn" class="btn theme-btn" type="submit" wire:loading.attr='disabled'>
                            <i class="fa fa-save fa-lg"></i>
                            Enregistrer
                        </button>
                    </div>
                </div>
            </div>
    </form>
</div>


<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/livewire/admin/location-meublee/create.blade.php ENDPATH**/ ?>