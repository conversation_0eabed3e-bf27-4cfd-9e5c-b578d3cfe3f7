

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'mapId' => 'map',
    'center' => ['lat' => 8.6195, 'lng' => 0.8248],
    'zoom' => 6,
    'clickable' => true,
    'withSelect2' => true,
    'withStepRefresh' => true
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'mapId' => 'map',
    'center' => ['lat' => 8.6195, 'lng' => 0.8248],
    'zoom' => 6,
    'clickable' => true,
    'withSelect2' => true,
    'withStepRefresh' => true
]); ?>
<?php foreach (array_filter(([
    'mapId' => 'map',
    'center' => ['lat' => 8.6195, 'lng' => 0.8248],
    'zoom' => 6,
    'clickable' => true,
    'withSelect2' => true,
    'withStepRefresh' => true
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    var mymap;
    var marker;
    var mapHelper;

    document.addEventListener('livewire:initialized', function () {
        console.log('Initializing admin creation form scripts...');
        
        // Initialize Google Maps using global system
        mapHelper = initGoogleMap('<?php echo e($mapId); ?>', {
            center: { lat: <?php echo e($center['lat']); ?>, lng: <?php echo e($center['lng']); ?> },
            zoom: <?php echo e($zoom); ?>,
            clickable: <?php echo e($clickable ? 'true' : 'false'); ?>

        }, function(helper, map) {
            mymap = map;
            marker = helper.marker;
            console.log('Admin creation form map initialized');

            <!--[if BLOCK]><![endif]--><?php if($withStepRefresh): ?>
            // Refresh map when step changes
            Livewire.on('stepChanged', () => {
                console.log('Step changed, refreshing map...');
                setTimeout(() => {
                    helper.refresh();
                }, 100);
            });
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        });
    });

    // <!--[if BLOCK]><![endif]--><?php if($withSelect2): ?>
    // $(document).ready(function() {
    //     console.log('Initializing Select2 for admin creation form...');
        
    //     $('.select2').select2({
    //         height: '25px',
    //         width: '100%',
    //     });

    //     $('.select2').on('change', function(e) {
    //         var data = $(this).val();
    //         var nom = $(this).data('nom');
    //         console.log('Select2 changed:', nom, '=', data);
    //         window.Livewire.find('<?php echo e($_instance->getId()); ?>').set(nom, data);
    //     });
    // });
    // <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-annonce\resources\views/components/admin/creation-form-scripts.blade.php ENDPATH**/ ?>