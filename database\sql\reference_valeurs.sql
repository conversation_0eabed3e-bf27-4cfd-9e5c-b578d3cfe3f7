INSERT INTO
    `pays` (
        `id`,
        `nom`,
        `slug`,
        `code`,
        `indicatif`,
        `langue`,
        `created_at`,
        `updated_at`,
        `deleted_at`,
        `created_by`,
        `updated_by`,
        `deleted_by`
    )
VALUES (
        1,
        'Togo',
        'togo',
        'TG',
        '+228',
        'Français',
        '2023-12-12 12:12:42',
        '2025-02-08 13:08:45',
        NULL,
        1,
        1,
        NULL
    ),
    (
        2,
        'France',
        'france',
        'FR',
        '+33',
        'Français',
        '2025-02-03 19:30:03',
        '2025-02-03 19:31:23',
        '2025-02-03 19:31:23',
        1,
        1,
        1
    ),
    (
        3,
        '<PERSON>én<PERSON>',
        'benin',
        'BN',
        '+229',
        'Français',
        '2025-02-08 13:03:37',
        '2025-05-02 16:33:36',
        NULL,
        1,
        1,
        NULL
    );

INSERT INTO
    `villes` (
        `id`,
        `nom`,
        `slug`,
        `pays_id`,
        `created_at`,
        `updated_at`,
        `deleted_at`,
        `created_by`,
        `updated_by`,
        `deleted_by`
    )
VALUES (
        1,
        'Lo<PERSON>',
        'lome',
        1,
        '2023-12-12 12:12:42',
        '2023-12-12 12:12:42',
        NULL,
        1,
        1,
        NULL
    ),
    (
        2,
        'Tsévié',
        'tsevie',
        1,
        '2025-01-21 08:24:43',
        '2025-01-21 08:24:43',
        NULL,
        21,
        21,
        NULL
    ),
    (
        3,
        'Sokod<PERSON>',
        'sokode',
        1,
        '2025-01-21 08:24:43',
        '2025-01-21 08:24:43',
        NULL,
        21,
        21,
        NULL
    ),
    (
        4,
        'Aného',
        'aneho',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        5,
        'Tabligbo',
        'tabligbo',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        6,
        'Kévé',
        'keve',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        7,
        'Vogan',
        'vogan',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        8,
        'Baguida',
        'baguida',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        9,
        'Aflao Gakli',
        'aflao-gakli',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        10,
        'Agbodrafo',
        'agbodrafo',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        11,
        'Avépozo',
        'avepozo',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        12,
        'Togoville',
        'togoville',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        13,
        'Zanguéra',
        'zanguera',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        14,
        'Glidji',
        'glidji',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        15,
        'Mission Tové',
        'mission-tove',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        16,
        'Djagblé',
        'djagble',
        1,
        '2025-01-21 08:31:37',
        '2025-01-21 08:31:37',
        NULL,
        21,
        21,
        NULL
    ),
    (
        17,
        'Adétikopé',
        'adetikope',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        18,
        'Atakpamé',
        'atakpame',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        19,
        'Kpalimé',
        'kpalime',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        20,
        'Badou',
        'badou',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        21,
        'Notsé',
        'notse',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        22,
        'Amlamé',
        'amlame',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        23,
        'Agou',
        'agou',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        24,
        'Danyi',
        'danyi',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        25,
        'Amou Oblo',
        'amou-oblo',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        26,
        'Kébo',
        'kebo',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        27,
        'Akloa',
        'akloa',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        28,
        'Kougnohou',
        'kougnohou',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        29,
        'Gboto',
        'gboto',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        30,
        'Glei',
        'glei',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        31,
        'Hihéatro',
        'hiheatro',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        32,
        'Tohoun',
        'tohoun',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        33,
        'Tchamba',
        'tchamba',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        34,
        'Blitta',
        'blitta',
        1,
        '2025-01-21 08:31:50',
        '2025-01-21 08:31:50',
        NULL,
        21,
        21,
        NULL
    ),
    (
        35,
        'Sotouboua',
        'sotouboua',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        36,
        'Alédjo-Kadara',
        'aledjo-kadara',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        37,
        'Kri-Kri',
        'kri-kri',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        38,
        'Défalé',
        'defale',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        39,
        'Adjengré',
        'adjengre',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        40,
        'Langabou',
        'langabou',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        41,
        'Tcharé',
        'tchare',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        42,
        'Fazao',
        'fazao',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        43,
        'Kara',
        'kara',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        44,
        'Bafilo',
        'bafilo',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        45,
        'Bassar',
        'bassar',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        46,
        'Niamtougou',
        'niamtougou',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        47,
        'Pagouda',
        'pagouda',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        48,
        'Kandé',
        'kande',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        49,
        'Kétao',
        'ketao',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        50,
        'Koutammakou',
        'koutammakou',
        1,
        '2025-01-21 08:32:14',
        '2025-01-21 08:32:14',
        NULL,
        21,
        21,
        NULL
    ),
    (
        51,
        'Kabou',
        'kabou',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        52,
        'Guérin Kouka',
        'guerin-kouka',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        53,
        'Pya',
        'pya',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        54,
        'Tchitchao',
        'tchitchao',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        55,
        'Boufalé',
        'boufale',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        56,
        'Dapaong',
        'dapaong',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        57,
        'Cinkassé',
        'cinkasse',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        58,
        'Mango',
        'mango',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        59,
        'Tandjouaré',
        'tandjouare',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        60,
        'Mandouri',
        'mandouri',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        61,
        'Korbongou',
        'korbongou',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        62,
        'Bogou',
        'bogou',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        63,
        'Gando',
        'gando',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        64,
        'Tami',
        'tami',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        65,
        'Barkoissi',
        'barkoissi',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        66,
        'Namoundjoga',
        'namoundjoga',
        1,
        '2025-01-21 08:32:28',
        '2025-01-21 08:32:28',
        NULL,
        21,
        21,
        NULL
    ),
    (
        67,
        'Paris',
        'paris',
        2,
        '2025-02-03 19:30:19',
        '2025-02-03 19:31:23',
        '2025-02-03 19:31:23',
        1,
        1,
        1
    ),
    (
        68,
        'Abomey Agbangnizoun Bohicon Covè Djidja Ouinhi Za-Kpota Zogbodomey Ifangni Kétou Pobè Sakété Adja-Ouèrè Adjarra Adjohoun',
        'abomey-agbangnizoun-bohicon-cove-djidja-ouinhi-za-kpota-zogbodomey-ifangni-ketou-pobe-sakete-adja-ouere-adjarra-adjohoun',
        3,
        '2025-02-08 13:22:32',
        '2025-02-08 13:23:12',
        '2025-02-08 13:23:12',
        1,
        1,
        1
    ),
    (
        69,
        'Abomey',
        'abomey',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        70,
        'Abomey-Calavi',
        'abomey-calavi',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        71,
        'Adja-Ouèrè',
        'adja-ouere',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        72,
        'Adjarra',
        'adjarra',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        73,
        'Adjohoun',
        'adjohoun',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        74,
        'Aguégués',
        'aguegues',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        75,
        'Akpro-Missérété',
        'akpro-misserete',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        76,
        'Allada',
        'allada',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        77,
        'Aplahoué',
        'aplahoue',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        78,
        'Athiémé',
        'athieme',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        79,
        'Avrankou',
        'avrankou',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        80,
        'Banikoara',
        'banikoara',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        81,
        'Bassila',
        'bassila',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        82,
        'Bembéréké',
        'bembereke',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        83,
        'Bohicon',
        'bohicon',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        84,
        'Bopa',
        'bopa',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        85,
        'Bonou',
        'bonou',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        86,
        'Boukombé',
        'boukombe',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        87,
        'Cobly',
        'cobly',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        88,
        'Comé',
        'come',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        89,
        'Copargo',
        'copargo',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        90,
        'Cotonou',
        'cotonou',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        91,
        'Covè',
        'cove',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        92,
        'Dangbo',
        'dangbo',
        3,
        '2025-02-08 13:24:36',
        '2025-02-08 13:24:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        93,
        'Dassa-Zoumè',
        'dassa-zoume',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        94,
        'Djakotomey',
        'djakotomey',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        95,
        'Djidja',
        'djidja',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        96,
        'Djougou',
        'djougou',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        97,
        'Dogbo',
        'dogbo',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        98,
        'Gogounou',
        'gogounou',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        99,
        'Glazoué',
        'glazoue',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        100,
        'Grand-Popo',
        'grand-popo',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        101,
        'Houéyogbé',
        'houeyogbe',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        102,
        'Ifangni',
        'ifangni',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        103,
        'Kalalé',
        'kalale',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        104,
        'Kandi',
        'kandi',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        105,
        'Karimama',
        'karimama',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        106,
        'Kérou',
        'kerou',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        107,
        'Kétou',
        'ketou',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        108,
        'Klouékanmè',
        'klouekanme',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        109,
        'Kouandé',
        'kouande',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        110,
        'Kpomassè',
        'kpomasse',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        111,
        'Lalo',
        'lalo',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        112,
        'Lokossa',
        'lokossa',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        113,
        'Malanville',
        'malanville',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        114,
        'Matéri',
        'materi',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        115,
        'N\'Dali',
        'ndali',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        116,
        'Natitingou',
        'natitingou',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        117,
        'Nikki',
        'nikki',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        118,
        'Ouinhi',
        'ouinhi',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        119,
        'Ouaké',
        'ouake',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        120,
        'Ouèssè',
        'ouesse',
        3,
        '2025-02-08 13:24:57',
        '2025-02-08 13:24:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        121,
        'Ouidah',
        'ouidah',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        122,
        'Péhunco',
        'pehunco',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        123,
        'Parakou',
        'parakou',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        124,
        'Pèrèrè',
        'perere',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        125,
        'Pobè',
        'pobe',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        126,
        'Porto-Novo',
        'porto-novo',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        127,
        'Sakété',
        'sakete',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        128,
        'Savalou',
        'savalou',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        129,
        'Savè',
        'save',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        130,
        'Ségbana',
        'segbana',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        131,
        'Sèmè-Kpodji',
        'seme-kpodji',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        132,
        'Sinendé',
        'sinende',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        133,
        'Sô-Ava',
        'so-ava',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        134,
        'Tanguiéta',
        'tanguieta',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        135,
        'Tchaourou',
        'tchaourou',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        136,
        'Toffo',
        'toffo',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        137,
        'Toribossito',
        'toribossito',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        138,
        'Toucountouna',
        'toucountouna',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        139,
        'Toviklin',
        'toviklin',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        140,
        'Zè',
        'ze',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        141,
        'Za-Kpota',
        'za-kpota',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    ),
    (
        142,
        'Zogbodomey',
        'zogbodomey',
        3,
        '2025-02-08 13:25:13',
        '2025-02-08 13:25:13',
        NULL,
        1,
        1,
        NULL
    );

INSERT INTO
    `quartiers` (
        `id`,
        `nom`,
        `slug`,
        `ville_id`,
        `created_at`,
        `updated_at`,
        `deleted_at`,
        `created_by`,
        `updated_by`,
        `deleted_by`
    )
VALUES (
        1,
        'Avedji',
        'avedji',
        1,
        '2023-12-12 12:12:42',
        '2023-12-12 12:12:42',
        NULL,
        1,
        1,
        NULL
    ),
    (
        2,
        'Adidogomé',
        'adidogome',
        1,
        '2024-02-03 20:32:57',
        '2024-02-03 20:32:57',
        NULL,
        2,
        2,
        NULL
    ),
    (
        3,
        'Soviépé',
        'soviepe',
        1,
        '2024-02-03 20:33:12',
        '2024-02-03 20:33:12',
        NULL,
        2,
        2,
        NULL
    ),
    (
        4,
        'Soviépé, Totsi',
        'soviepe-totsi',
        1,
        '2024-12-09 15:31:19',
        '2024-12-09 15:31:19',
        NULL,
        21,
        21,
        NULL
    ),
    (
        5,
        'q1',
        'q1',
        67,
        '2025-02-03 19:31:06',
        '2025-02-03 19:31:23',
        '2025-02-03 19:31:23',
        1,
        1,
        1
    ),
    (
        6,
        'q2',
        'q2',
        67,
        '2025-02-03 19:31:06',
        '2025-02-03 19:31:23',
        '2025-02-03 19:31:23',
        1,
        1,
        1
    ),
    (
        7,
        'Zanguéra',
        'zanguera',
        1,
        '2025-02-16 06:40:17',
        '2025-02-16 06:40:17',
        NULL,
        1,
        1,
        NULL
    ),
    (
        8,
        'Midoudou',
        'midoudou',
        18,
        '2025-02-20 06:31:38',
        '2025-02-20 06:31:38',
        NULL,
        1,
        1,
        NULL
    ),
    (
        9,
        'Campement',
        'campement',
        18,
        '2025-02-20 06:36:57',
        '2025-02-20 06:36:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        10,
        'Tsévié 1',
        'tsevie-1',
        2,
        '2025-03-01 07:09:14',
        '2025-03-01 07:09:14',
        NULL,
        1,
        1,
        NULL
    ),
    (
        11,
        'Ségbé',
        'segbe',
        10,
        '2025-03-04 08:06:11',
        '2025-03-04 08:06:11',
        NULL,
        1,
        1,
        NULL
    ),
    (
        12,
        'Kpogan',
        'kpogan',
        1,
        '2025-03-07 06:11:36',
        '2025-03-07 06:11:36',
        NULL,
        1,
        1,
        NULL
    ),
    (
        13,
        'Badou',
        'badou',
        18,
        '2025-04-14 20:48:42',
        '2025-04-14 20:48:42',
        NULL,
        3,
        3,
        NULL
    ),
    (
        14,
        'Tofo',
        'tofo',
        81,
        '2025-04-16 17:40:04',
        '2025-04-16 17:40:04',
        NULL,
        1,
        1,
        NULL
    ),
    (
        15,
        'Fiokomé',
        'fiokome',
        4,
        '2025-04-25 15:56:44',
        '2025-04-25 15:56:44',
        NULL,
        10,
        10,
        NULL
    ),
    (
        16,
        'Lawson come',
        'lawson-come',
        4,
        '2025-05-13 21:01:30',
        '2025-05-13 21:01:30',
        NULL,
        23,
        23,
        NULL
    ),
    (
        17,
        'Ségbé',
        'segbe',
        11,
        '2025-05-16 08:40:09',
        '2025-05-16 08:40:09',
        NULL,
        1,
        1,
        NULL
    ),
    (
        18,
        'Gbéssé',
        'gbesse',
        4,
        '2025-05-16 08:47:55',
        '2025-05-16 08:47:55',
        NULL,
        1,
        1,
        NULL
    ),
    (
        19,
        'Adidogomé',
        'adidogome',
        81,
        '2025-05-16 08:49:50',
        '2025-05-16 08:49:50',
        NULL,
        1,
        1,
        NULL
    ),
    (
        20,
        'Ségbé',
        'segbe',
        20,
        '2025-05-16 09:00:31',
        '2025-05-16 09:00:31',
        NULL,
        1,
        1,
        NULL
    ),
    (
        21,
        'Soviépé',
        'soviepe',
        81,
        '2025-05-16 09:12:06',
        '2025-05-16 09:12:06',
        NULL,
        1,
        1,
        NULL
    );

INSERT INTO
    `marques` (
        `id`,
        `nom`,
        `slug`,
        `description`,
        `is_active`,
        `created_at`,
        `updated_at`,
        `created_by`,
        `updated_by`
    )
VALUES (
        2,
        'Fiat',
        'fiat',
        NULL,
        1,
        '2025-02-04 19:41:14',
        '2025-02-04 19:41:14',
        1,
        1
    ),
    (
        3,
        'Toyota',
        'toyota',
        NULL,
        1,
        '2025-02-07 09:15:21',
        '2025-02-07 09:15:21',
        1,
        1
    ),
    (
        4,
        'Hyundai',
        'hyundai',
        NULL,
        1,
        '2025-02-07 09:15:21',
        '2025-02-07 09:15:21',
        1,
        1
    ),
    (
        5,
        'Nissan',
        'nissan',
        NULL,
        1,
        '2025-02-07 09:15:21',
        '2025-02-07 09:15:21',
        1,
        1
    ),
    (
        6,
        'Mazda',
        'mazda',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        7,
        'BMW',
        'bmw',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        8,
        'Mercedes-Benz',
        'mercedes-benz',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        9,
        'Volkswagen',
        'volkswagen',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        10,
        'Kia',
        'kia',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        11,
        'Renault',
        'renault',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        12,
        'Peugeot',
        'peugeot',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        13,
        'Mitsubishi',
        'mitsubishi',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        14,
        'Ford',
        'ford',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        15,
        'Land Rover',
        'land-rover',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        16,
        'Opel',
        'opel',
        NULL,
        1,
        '2025-02-07 09:15:22',
        '2025-02-07 09:15:22',
        1,
        1
    ),
    (
        17,
        'Suzuki',
        'suzuki',
        NULL,
        1,
        '2025-02-07 11:40:03',
        '2025-02-07 11:40:03',
        1,
        1
    ),
    (
        18,
        'Skoda',
        'skoda',
        NULL,
        1,
        '2025-02-07 11:40:03',
        '2025-02-07 11:40:03',
        1,
        1
    ),
    (
        19,
        'Citroën',
        'citroen',
        NULL,
        1,
        '2025-02-07 11:40:03',
        '2025-02-07 11:40:03',
        1,
        1
    ),
    (
        20,
        'Dacia',
        'dacia',
        NULL,
        1,
        '2025-02-07 11:40:03',
        '2025-02-07 11:40:03',
        1,
        1
    ),
    (
        21,
        'Honda',
        'honda',
        NULL,
        1,
        '2025-02-07 11:40:03',
        '2025-02-07 11:40:03',
        1,
        1
    ),
    (
        22,
        'Audi',
        'audi',
        NULL,
        1,
        '2025-02-07 11:40:03',
        '2025-02-07 11:40:03',
        1,
        1
    );

INSERT INTO
    `modeles` (
        `id`,
        `nom`,
        `slug`,
        `description`,
        `is_active`,
        `marque_id`,
        `created_at`,
        `updated_at`,
        `created_by`,
        `updated_by`
    )
VALUES (
        2,
        'Bravo',
        'bravo',
        NULL,
        1,
        2,
        '2025-02-04 19:41:27',
        '2025-02-04 19:41:27',
        1,
        1
    ),
    (
        4,
        'Picanto',
        'picanto',
        NULL,
        1,
        2,
        '2025-02-04 19:41:59',
        '2025-02-04 19:41:59',
        1,
        1
    ),
    (
        5,
        'Corolla',
        'corolla',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        6,
        'Avensis',
        'avensis',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        7,
        'Yaris',
        'yaris',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        8,
        'Camry',
        'camry',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        9,
        'Vitz',
        'vitz',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        10,
        'RAV4',
        'rav4',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        11,
        'Fortuner',
        'fortuner',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        12,
        'Land Cruiser Prado',
        'land-cruiser-prado',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        13,
        'Land Cruiser 70',
        'land-cruiser-70',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        14,
        'Land Cruiser V8',
        'land-cruiser-v8',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        15,
        'Hilux',
        'hilux',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        16,
        'Tacoma',
        'tacoma',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        17,
        'Tundra',
        'tundra',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        18,
        'HiAce',
        'hiace',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        19,
        'Coaster',
        'coaster',
        NULL,
        1,
        3,
        '2025-02-07 11:40:38',
        '2025-02-07 11:40:38',
        1,
        1
    ),
    (
        20,
        'i10',
        'i10',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        21,
        'i20',
        'i20',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        22,
        'i30',
        'i30',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        23,
        'Elantra',
        'elantra',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        24,
        'Sonata',
        'sonata',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        25,
        'Tucson',
        'tucson',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        26,
        'Santa Fe',
        'santa-fe',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        27,
        'Creta',
        'creta',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        28,
        'Palisade',
        'palisade',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        29,
        'H-100',
        'h-100',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        30,
        'Porter II',
        'porter-ii',
        NULL,
        1,
        4,
        '2025-02-07 11:40:57',
        '2025-02-07 11:40:57',
        1,
        1
    ),
    (
        31,
        'Sunny',
        'sunny',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        32,
        'Almera',
        'almera',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        33,
        'Sentra',
        'sentra',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        34,
        'Maxima',
        'maxima',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        35,
        'Qashqai',
        'qashqai',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        36,
        'X-Trail',
        'x-trail',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        37,
        'Pathfinder',
        'pathfinder',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        38,
        'Patrol',
        'patrol',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        39,
        'Terrano',
        'terrano',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        40,
        'Navara',
        'navara',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        41,
        'Frontier',
        'frontier',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        42,
        'Hardbody',
        'hardbody',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        43,
        'Urvan',
        'urvan',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        44,
        'Caravan',
        'caravan',
        NULL,
        1,
        5,
        '2025-02-07 11:41:14',
        '2025-02-07 11:41:14',
        1,
        1
    ),
    (
        45,
        'Mazda 323',
        'mazda-323',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        46,
        'Mazda2',
        'mazda2',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        47,
        'Mazda3',
        'mazda3',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        48,
        'Mazda6',
        'mazda6',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        49,
        'CX-3',
        'cx-3',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        50,
        'CX-5',
        'cx-5',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        51,
        'CX-9',
        'cx-9',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        52,
        'BT-50',
        'bt-50',
        NULL,
        1,
        6,
        '2025-02-07 11:41:36',
        '2025-02-07 11:41:36',
        1,
        1
    ),
    (
        53,
        'Série 1',
        'serie-1',
        NULL,
        1,
        7,
        '2025-02-07 11:41:52',
        '2025-02-07 11:41:52',
        1,
        1
    ),
    (
        54,
        'Série 3',
        'serie-3',
        NULL,
        1,
        7,
        '2025-02-07 11:41:52',
        '2025-02-07 11:41:52',
        1,
        1
    ),
    (
        55,
        'Série 5',
        'serie-5',
        NULL,
        1,
        7,
        '2025-02-07 11:41:52',
        '2025-02-07 11:41:52',
        1,
        1
    ),
    (
        56,
        'Série 7',
        'serie-7',
        NULL,
        1,
        7,
        '2025-02-07 11:41:52',
        '2025-02-07 11:41:52',
        1,
        1
    ),
    (
        57,
        'X1',
        'x1',
        NULL,
        1,
        7,
        '2025-02-07 11:41:53',
        '2025-02-07 11:41:53',
        1,
        1
    ),
    (
        58,
        'X3',
        'x3',
        NULL,
        1,
        7,
        '2025-02-07 11:41:53',
        '2025-02-07 11:41:53',
        1,
        1
    ),
    (
        59,
        'X5',
        'x5',
        NULL,
        1,
        7,
        '2025-02-07 11:41:53',
        '2025-02-07 11:41:53',
        1,
        1
    ),
    (
        60,
        'X6',
        'x6',
        NULL,
        1,
        7,
        '2025-02-07 11:41:53',
        '2025-02-07 11:41:53',
        1,
        1
    ),
    (
        61,
        'Classe A',
        'classe-a',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        62,
        'Classe C',
        'classe-c',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        63,
        'Classe E',
        'classe-e',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        64,
        'Classe S',
        'classe-s',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        65,
        'GLA',
        'gla',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        66,
        'GLC',
        'glc',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        67,
        'GLE',
        'gle',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        68,
        'GLS',
        'gls',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        69,
        'Classe G',
        'classe-g',
        NULL,
        1,
        8,
        '2025-02-07 11:42:16',
        '2025-02-07 11:42:16',
        1,
        1
    ),
    (
        70,
        'Polo',
        'polo',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        71,
        'Golf',
        'golf',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        72,
        'Passat',
        'passat',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        73,
        'Jetta',
        'jetta',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        74,
        'Tiguan',
        'tiguan',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        75,
        'Touareg',
        'touareg',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        76,
        'T-Roc',
        't-roc',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        77,
        'Transporter',
        'transporter',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        78,
        'Amarok',
        'amarok',
        NULL,
        1,
        9,
        '2025-02-07 11:42:31',
        '2025-02-07 11:42:31',
        1,
        1
    ),
    (
        79,
        'Picanto',
        'picanto',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        80,
        'Rio',
        'rio',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        81,
        'Ceed',
        'ceed',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        82,
        'Forte',
        'forte',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        83,
        'Sportage',
        'sportage',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        84,
        'Sorento',
        'sorento',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        85,
        'Seltos',
        'seltos',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        86,
        'K2700',
        'k2700',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        87,
        'K2500',
        'k2500',
        NULL,
        1,
        10,
        '2025-02-07 11:42:45',
        '2025-02-07 11:42:45',
        1,
        1
    ),
    (
        88,
        'Clio',
        'clio',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        89,
        'Logan',
        'logan',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        90,
        'Symbol',
        'symbol',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        91,
        'Duster',
        'duster',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        92,
        'Koleos',
        'koleos',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        93,
        'Captur',
        'captur',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        94,
        'Trafic',
        'trafic',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        95,
        'Master',
        'master',
        NULL,
        1,
        11,
        '2025-02-07 11:43:01',
        '2025-02-07 11:43:01',
        1,
        1
    ),
    (
        96,
        '206',
        '206',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        97,
        '207',
        '207',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        98,
        '208',
        '208',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        99,
        '301',
        '301',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        100,
        '308',
        '308',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        101,
        '508',
        '508',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        102,
        '2008',
        '2008',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        103,
        '3008',
        '3008',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        104,
        '5008',
        '5008',
        NULL,
        1,
        12,
        '2025-02-07 11:43:15',
        '2025-02-07 11:43:15',
        1,
        1
    ),
    (
        105,
        'Pajero',
        'pajero',
        NULL,
        1,
        13,
        '2025-02-07 11:43:30',
        '2025-02-07 11:43:30',
        1,
        1
    ),
    (
        106,
        'Montero',
        'montero',
        NULL,
        1,
        13,
        '2025-02-07 11:43:30',
        '2025-02-07 11:43:30',
        1,
        1
    ),
    (
        107,
        'Outlander',
        'outlander',
        NULL,
        1,
        13,
        '2025-02-07 11:43:30',
        '2025-02-07 11:43:30',
        1,
        1
    ),
    (
        108,
        'L200',
        'l200',
        NULL,
        1,
        13,
        '2025-02-07 11:43:30',
        '2025-02-07 11:43:30',
        1,
        1
    ),
    (
        109,
        'Triton',
        'triton',
        NULL,
        1,
        13,
        '2025-02-07 11:43:31',
        '2025-02-07 11:43:31',
        1,
        1
    ),
    (
        110,
        'Canter',
        'canter',
        NULL,
        1,
        13,
        '2025-02-07 11:43:31',
        '2025-02-07 11:43:31',
        1,
        1
    ),
    (
        111,
        'Fiesta',
        'fiesta',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        112,
        'Focus',
        'focus',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        113,
        'Fusion',
        'fusion',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        114,
        'Escape',
        'escape',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        115,
        'Explorer',
        'explorer',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        116,
        'Edge',
        'edge',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        117,
        'Ranger',
        'ranger',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        118,
        'F-150',
        'f-150',
        NULL,
        1,
        14,
        '2025-02-07 11:43:45',
        '2025-02-07 11:43:45',
        1,
        1
    ),
    (
        119,
        'Range Rover',
        'range-rover',
        NULL,
        1,
        15,
        '2025-02-07 11:43:58',
        '2025-02-07 11:43:58',
        1,
        1
    ),
    (
        120,
        'Range Rover Sport',
        'range-rover-sport',
        NULL,
        1,
        15,
        '2025-02-07 11:43:58',
        '2025-02-07 11:43:58',
        1,
        1
    ),
    (
        121,
        'Discovery',
        'discovery',
        NULL,
        1,
        15,
        '2025-02-07 11:43:58',
        '2025-02-07 11:43:58',
        1,
        1
    ),
    (
        122,
        'Defender',
        'defender',
        NULL,
        1,
        15,
        '2025-02-07 11:43:58',
        '2025-02-07 11:43:58',
        1,
        1
    ),
    (
        123,
        'Corsa',
        'corsa',
        NULL,
        1,
        16,
        '2025-02-07 11:44:12',
        '2025-02-07 11:44:12',
        1,
        1
    ),
    (
        124,
        'Astra',
        'astra',
        NULL,
        1,
        16,
        '2025-02-07 11:44:12',
        '2025-02-07 11:44:12',
        1,
        1
    ),
    (
        125,
        'Insignia',
        'insignia',
        NULL,
        1,
        16,
        '2025-02-07 11:44:12',
        '2025-02-07 11:44:12',
        1,
        1
    ),
    (
        126,
        'Mokka',
        'mokka',
        NULL,
        1,
        16,
        '2025-02-07 11:44:12',
        '2025-02-07 11:44:12',
        1,
        1
    ),
    (
        127,
        'Grandland X',
        'grandland-x',
        NULL,
        1,
        16,
        '2025-02-07 11:44:12',
        '2025-02-07 11:44:12',
        1,
        1
    ),
    (
        128,
        'Swift',
        'swift',
        NULL,
        1,
        17,
        '2025-02-07 11:44:25',
        '2025-02-07 11:44:25',
        1,
        1
    ),
    (
        129,
        'Vitara',
        'vitara',
        NULL,
        1,
        17,
        '2025-02-07 11:44:25',
        '2025-02-07 11:44:25',
        1,
        1
    ),
    (
        130,
        'Jimny',
        'jimny',
        NULL,
        1,
        17,
        '2025-02-07 11:44:25',
        '2025-02-07 11:44:25',
        1,
        1
    ),
    (
        131,
        'S-Cross',
        's-cross',
        NULL,
        1,
        17,
        '2025-02-07 11:44:25',
        '2025-02-07 11:44:25',
        1,
        1
    ),
    (
        132,
        'Ignis',
        'ignis',
        NULL,
        1,
        17,
        '2025-02-07 11:44:25',
        '2025-02-07 11:44:25',
        1,
        1
    ),
    (
        133,
        'Octavia',
        'octavia',
        NULL,
        1,
        18,
        '2025-02-07 11:44:40',
        '2025-02-07 11:44:40',
        1,
        1
    ),
    (
        134,
        'Superb',
        'superb',
        NULL,
        1,
        18,
        '2025-02-07 11:44:40',
        '2025-02-07 11:44:40',
        1,
        1
    ),
    (
        135,
        'Kodiaq',
        'kodiaq',
        NULL,
        1,
        18,
        '2025-02-07 11:44:40',
        '2025-02-07 11:44:40',
        1,
        1
    ),
    (
        136,
        'Karoq',
        'karoq',
        NULL,
        1,
        18,
        '2025-02-07 11:44:40',
        '2025-02-07 11:44:40',
        1,
        1
    ),
    (
        137,
        'Fabia',
        'fabia',
        NULL,
        1,
        18,
        '2025-02-07 11:44:40',
        '2025-02-07 11:44:40',
        1,
        1
    ),
    (
        138,
        'C3',
        'c3',
        NULL,
        1,
        19,
        '2025-02-07 11:45:09',
        '2025-02-07 11:45:09',
        1,
        1
    ),
    (
        139,
        'C4',
        'c4',
        NULL,
        1,
        19,
        '2025-02-07 11:45:09',
        '2025-02-07 11:45:09',
        1,
        1
    ),
    (
        140,
        'C5 Aircross',
        'c5-aircross',
        NULL,
        1,
        19,
        '2025-02-07 11:45:09',
        '2025-02-07 11:45:09',
        1,
        1
    ),
    (
        141,
        'Berlingo',
        'berlingo',
        NULL,
        1,
        19,
        '2025-02-07 11:45:09',
        '2025-02-07 11:45:09',
        1,
        1
    ),
    (
        142,
        'ë-C4',
        'e-c4',
        NULL,
        1,
        19,
        '2025-02-07 11:45:09',
        '2025-02-07 11:45:09',
        1,
        1
    ),
    (
        143,
        'Sandero',
        'sandero',
        NULL,
        1,
        20,
        '2025-02-07 11:45:22',
        '2025-02-07 11:45:22',
        1,
        1
    ),
    (
        144,
        'Duster',
        'duster',
        NULL,
        1,
        20,
        '2025-02-07 11:45:22',
        '2025-02-07 11:45:22',
        1,
        1
    ),
    (
        145,
        'Logan',
        'logan',
        NULL,
        1,
        20,
        '2025-02-07 11:45:22',
        '2025-02-07 11:45:22',
        1,
        1
    ),
    (
        146,
        'Spring',
        'spring',
        NULL,
        1,
        20,
        '2025-02-07 11:45:22',
        '2025-02-07 11:45:22',
        1,
        1
    ),
    (
        147,
        'Civic',
        'civic',
        NULL,
        1,
        21,
        '2025-02-07 11:45:33',
        '2025-02-07 11:45:33',
        1,
        1
    ),
    (
        148,
        'Accord',
        'accord',
        NULL,
        1,
        21,
        '2025-02-07 11:45:33',
        '2025-02-07 11:45:33',
        1,
        1
    ),
    (
        149,
        'CR-V',
        'cr-v',
        NULL,
        1,
        21,
        '2025-02-07 11:45:33',
        '2025-02-07 11:45:33',
        1,
        1
    ),
    (
        150,
        'HR-V',
        'hr-v',
        NULL,
        1,
        21,
        '2025-02-07 11:45:33',
        '2025-02-07 11:45:33',
        1,
        1
    ),
    (
        151,
        'Jazz',
        'jazz',
        NULL,
        1,
        21,
        '2025-02-07 11:45:33',
        '2025-02-07 11:45:33',
        1,
        1
    ),
    (
        152,
        'NSX',
        'nsx',
        NULL,
        1,
        21,
        '2025-02-07 11:45:33',
        '2025-02-07 11:45:33',
        1,
        1
    ),
    (
        153,
        'A4',
        'a4',
        NULL,
        1,
        22,
        '2025-02-07 11:45:44',
        '2025-02-07 11:45:44',
        1,
        1
    ),
    (
        154,
        'A6',
        'a6',
        NULL,
        1,
        22,
        '2025-02-07 11:45:44',
        '2025-02-07 11:45:44',
        1,
        1
    ),
    (
        155,
        'Q5',
        'q5',
        NULL,
        1,
        22,
        '2025-02-07 11:45:44',
        '2025-02-07 11:45:44',
        1,
        1
    ),
    (
        156,
        'Q7',
        'q7',
        NULL,
        1,
        22,
        '2025-02-07 11:45:44',
        '2025-02-07 11:45:44',
        1,
        1
    ),
    (
        157,
        'TT',
        'tt',
        NULL,
        1,
        22,
        '2025-02-07 11:45:44',
        '2025-02-07 11:45:44',
        1,
        1
    ),
    (
        158,
        'R8',
        'r8',
        NULL,
        1,
        22,
        '2025-02-07 11:45:44',
        '2025-02-07 11:45:44',
        1,
        1
    ),
    (
        159,
        'e-tron',
        'e-tron',
        NULL,
        1,
        22,
        '2025-02-07 11:45:44',
        '2025-02-07 11:45:44',
        1,
        1
    );

INSERT INTO
    `references` (
        `id`,
        `type`,
        `slug_type`,
        `nom`,
        `slug_nom`,
        `created_at`,
        `updated_at`,
        `deleted_at`,
        `created_by`,
        `updated_by`,
        `deleted_by`
    )
VALUES (
        1,
        'Hébergement',
        'hebergement',
        'Commodités hébergement',
        'commodites-hebergement',
        '2024-11-20 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        2,
        'Hébergement',
        'hebergement',
        'Services proposés',
        'services-proposes',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        3,
        'Hébergement',
        'hebergement',
        'Types de lit',
        'types-de-lit',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        4,
        'Hébergement',
        'hebergement',
        'Equipements hébergement',
        'equipements-hebergement',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        5,
        'Hébergement',
        'hebergement',
        'Equipements salle de bain',
        'equipements-salle-de-bain',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        6,
        'Hébergement',
        'hebergement',
        'Accessoires de cuisine',
        'accessoires-de-cuisine',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        7,
        'Hébergement',
        'hebergement',
        'Types hebergement',
        'types-hebergement',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        8,
        'Location de véhicule',
        'location-de-vehicule',
        'Type de voiture',
        'types-de-voiture',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        9,
        'Location de véhicule',
        'location-de-vehicule',
        'Options et accesoires',
        'options-accessoires',
        '2024-11-22 06:43:32',
        '2024-11-22 06:43:32',
        NULL,
        1,
        1,
        NULL
    ),
    (
        10,
        'Restauration',
        'restauration',
        'Boissons disponibles',
        'boissons-disponibles',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        11,
        'Location de véhicule',
        'location-de-vehicule',
        'Boite de vitesses',
        'boite-de-vitesses',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        12,
        'Location de véhicule',
        'location-de-vehicule',
        'Conditions de location',
        'conditions-de-location',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        13,
        'Marque',
        'marque',
        'Marques de véhicule',
        'marques-de-vehicule',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        14,
        'Restauration',
        'restauration',
        'Equipements restauration',
        'equipements-restauration',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        15,
        'Restauration',
        'restauration',
        'Equipements patisserie',
        'equipements-patisserie',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        16,
        'Restauration',
        'restauration',
        'Produits fast-food',
        'produits-fast-food',
        '2024-11-22 06:43:33',
        '2025-02-20 05:39:22',
        '2025-02-20 05:39:22',
        1,
        1,
        NULL
    ),
    (
        17,
        'Restauration',
        'restauration',
        'Produits patissiers',
        'produits-patissiers',
        '2024-11-22 06:43:33',
        '2025-02-20 05:39:22',
        '2025-02-20 05:39:22',
        1,
        1,
        NULL
    ),
    (
        18,
        'Location de véhicule',
        'location-de-vehicule',
        'Type de moteur',
        'types-moteur',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        19,
        'Vie nocturne',
        'vie-nocturne',
        'Types de musique',
        'types-de-musique',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        20,
        'Vie nocturne',
        'vie-nocturne',
        'Commodités vie nocturne',
        'commodites-vie-nocturne',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        21,
        'Vie nocturne',
        'vie-nocturne',
        'Equipements vie nocturne',
        'equipements-vie-nocturne',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        22,
        'Restauration',
        'restauration',
        'Types de cuisine',
        'types-cuisine',
        '2024-11-22 06:43:33',
        '2024-11-22 06:43:33',
        NULL,
        1,
        1,
        NULL
    ),
    (
        23,
        'Vie nocturne',
        'vie-nocturne',
        'Services',
        'services',
        '2024-12-23 07:52:13',
        '2024-12-23 07:52:18',
        NULL,
        1,
        1,
        NULL
    ),
    (
        24,
        'Restauration',
        'restauration',
        'Services proposés',
        'services-proposes',
        '2024-12-23 08:06:05',
        '2024-12-23 08:06:07',
        NULL,
        1,
        1,
        NULL
    );

INSERT INTO
    `reference_valeurs` (
        `id`,
        `valeur`,
        `reference_id`,
        `created_at`,
        `updated_at`,
        `deleted_at`,
        `created_by`,
        `updated_by`,
        `deleted_by`
    )
VALUES (
        1,
        'Piscine',
        1,
        '2024-11-21 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        2,
        'Salle de sport',
        1,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        3,
        'Balcon/terrasse',
        1,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        4,
        'Jardin ou barbecue',
        1,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        5,
        'Jeux et divertissements',
        1,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        6,
        'Nettoyage d\'entrée/sortie',
        2,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        7,
        'Conciergerie',
        2,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        8,
        'Gardiennage',
        2,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        9,
        'Petit déjeuner compris',
        2,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        10,
        'Lit simple',
        3,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        11,
        'Lit double',
        3,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        12,
        'Queen size',
        3,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        13,
        'Lit bébé',
        3,
        '2024-11-22 06:59:53',
        '2024-12-11 07:06:38',
        '2024-12-11 07:06:38',
        1,
        1,
        1
    ),
    (
        14,
        'Lits superposés',
        3,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        15,
        'Placards, commodes, armoires',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        16,
        'Système de sécurité (caméras, alarmes)',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        17,
        'Connexion internet',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        18,
        'Détecteurs de fumée et de monoxyde de carbone',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        19,
        'Aspirateur ',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        20,
        'Multiprises et chargeurs',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        21,
        'Canapé',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        22,
        'Lave-linge.',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        23,
        'Sèche-linge (ou étendoir)',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        24,
        'Table et fer à repasser',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        25,
        'Kit de premiers secours',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        26,
        'Jeux ou jouets',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        27,
        'Climatisée',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        28,
        'Ventilée',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        29,
        'Téléviseur',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        30,
        'Imprimante',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        31,
        'Ordinateur',
        4,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        32,
        'Lavabo ou Vasque',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        33,
        'Cabine de douche',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        34,
        'Baignoire',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        35,
        'WC intégré salle de bain',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        36,
        'WC séparé',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        37,
        'Panier à linge',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        38,
        'Armoire de toilette',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        39,
        'Colonnes de rangement',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        40,
        'Eau chaude',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        41,
        'Tapis de bain',
        5,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        42,
        'Four',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        43,
        'Plaques de cuissons',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        44,
        'Plan de travail',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        45,
        'Réfrigérateur',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        46,
        'Lave-vaisselle',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        47,
        'Hotte aspirante',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        48,
        'Poubelle intégrée',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        49,
        'Micro-ondes',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        50,
        'Bouilloire',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        51,
        'Blendeur/mixeur',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        52,
        'Machine à café',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        53,
        'Grille-pain',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        54,
        'Friteuse',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        55,
        'Verres à vin',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        56,
        'Couverts',
        6,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        57,
        'Maison T4 (3 chambres salon)',
        7,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        58,
        'Maison T3 (2 chambres salon)',
        7,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        59,
        'Maison T2 (chambre salon)',
        7,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        60,
        'T1 ou Studio',
        7,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        61,
        'Appartement T4 (3 chambres)',
        7,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        62,
        'Appartement T3 (2 chambres)',
        7,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        63,
        'Appartement T2 (Chambre salon)',
        7,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        64,
        'Utilitaire ( Camion )',
        8,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        65,
        'Citadine',
        8,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        66,
        'Berline',
        8,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        67,
        'Familiale ( 7 places ) ',
        8,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        68,
        '4x4',
        8,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        69,
        'SUV',
        8,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        70,
        'Minibus',
        8,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        71,
        'Climatisation',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        72,
        'Sièges réglables ',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        73,
        'Accoudoir central',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        74,
        'Pare-soleil avec miroir éclairé',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        75,
        'Airbags (frontaux, latéraux, rideaux)',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        76,
        'Caméra de recul',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        77,
        'Freinage automatique d’urgence',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        78,
        'Navigation GPS',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        79,
        'Commandes vocales',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        80,
        'Connectivité Bluetooth et USB',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        81,
        'Apple CarPlay / Android Auto',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        82,
        'Pare-soleil intégrés',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        83,
        'Régulateur de vitesse',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        84,
        'Limiteur de vitesse',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        85,
        'Caméra 360°',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        86,
        'Toit ouvrant ou panoramique',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        87,
        'Feux antibrouillard',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        88,
        'Siège bébé ou fixation ISOFIX',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        89,
        'Espace de rangement sous le coffre',
        9,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        90,
        'Minimum 3 jours',
        12,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        91,
        '18 ans minimum',
        12,
        '2024-11-22 06:59:53',
        '2024-12-02 13:39:02',
        NULL,
        1,
        1,
        NULL
    ),
    (
        92,
        'Ancienneté de 2 ans de permis',
        12,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        93,
        'Ancienneté de plus de 3 ans de permis',
        12,
        '2024-11-22 06:59:53',
        '2024-12-02 13:33:34',
        NULL,
        1,
        1,
        NULL
    ),
    (
        94,
        'Ne doit pas traverser une frontière',
        12,
        '2024-11-22 06:59:53',
        '2024-11-22 06:59:53',
        NULL,
        1,
        1,
        NULL
    ),
    (
        95,
        'Spe1',
        22,
        '2024-11-22 07:57:27',
        '2024-11-22 11:48:59',
        '2024-11-22 11:48:59',
        1,
        1,
        1
    ),
    (
        96,
        'cwe',
        6,
        '2024-11-22 11:26:54',
        '2024-11-22 11:48:52',
        '2024-11-22 11:48:52',
        1,
        1,
        1
    ),
    (
        97,
        'fgbgbgb',
        6,
        '2024-11-22 11:52:59',
        '2024-11-22 11:53:51',
        '2024-11-22 11:53:51',
        1,
        1,
        1
    ),
    (
        98,
        'frfefefre',
        1,
        '2024-11-22 12:01:10',
        '2024-11-22 12:01:48',
        '2024-11-22 12:01:48',
        1,
        1,
        1
    ),
    (
        99,
        'cwecwcece',
        6,
        '2024-11-22 12:05:02',
        '2024-11-22 12:21:01',
        '2024-11-22 12:21:01',
        1,
        1,
        1
    ),
    (
        100,
        'cwececwe',
        20,
        '2024-11-22 12:05:09',
        '2024-11-22 12:20:53',
        '2024-11-22 12:20:53',
        1,
        1,
        1
    ),
    (
        101,
        'ecwece',
        4,
        '2024-11-22 12:05:51',
        '2024-11-22 12:20:48',
        '2024-11-22 12:20:48',
        1,
        1,
        1
    ),
    (
        102,
        'dwdwedew',
        6,
        '2024-11-22 12:06:03',
        '2024-11-22 12:20:45',
        '2024-11-22 12:20:45',
        1,
        1,
        1
    ),
    (
        103,
        'eerfrer',
        1,
        '2024-11-22 12:11:51',
        '2024-11-22 12:20:41',
        '2024-11-22 12:20:41',
        1,
        1,
        1
    ),
    (
        104,
        'cdcdcfc',
        6,
        '2024-11-22 12:12:27',
        '2024-11-22 12:20:37',
        '2024-11-22 12:20:37',
        1,
        1,
        1
    ),
    (
        105,
        'efrferfer',
        12,
        '2024-11-22 12:12:48',
        '2024-11-22 12:20:31',
        '2024-11-22 12:20:31',
        1,
        1,
        1
    ),
    (
        106,
        'scdscsd',
        1,
        '2024-11-22 12:18:08',
        '2024-11-22 12:19:04',
        '2024-11-22 12:19:04',
        1,
        1,
        1
    ),
    (
        107,
        'Commodité test 2',
        1,
        '2024-11-23 17:39:48',
        '2024-11-23 17:40:08',
        '2024-11-23 17:40:08',
        1,
        1,
        1
    ),
    (
        108,
        'TOYOTA',
        13,
        '2024-11-23 19:03:54',
        '2024-11-23 19:03:54',
        NULL,
        1,
        1,
        NULL
    ),
    (
        109,
        'Ras',
        6,
        '2024-11-23 19:05:59',
        '2024-12-02 11:51:28',
        '2024-12-02 11:51:28',
        1,
        1,
        1
    ),
    (
        110,
        'dwede',
        1,
        '2024-11-23 19:08:28',
        '2024-12-02 11:51:23',
        '2024-12-02 11:51:23',
        1,
        1,
        1
    ),
    (
        111,
        'dwde',
        1,
        '2024-11-23 19:09:50',
        '2024-12-02 11:51:19',
        '2024-12-02 11:51:19',
        1,
        1,
        1
    ),
    (
        112,
        'dewedw',
        12,
        '2024-11-23 19:10:53',
        '2024-12-02 11:51:13',
        '2024-12-02 11:51:13',
        1,
        1,
        1
    ),
    (
        113,
        'OPEL',
        13,
        '2024-12-19 13:27:26',
        '2024-12-19 13:27:26',
        NULL,
        1,
        1,
        NULL
    ),
    (
        114,
        'AUDI',
        13,
        '2024-12-19 13:27:40',
        '2024-12-19 13:27:40',
        NULL,
        1,
        1,
        NULL
    ),
    (
        115,
        'MAZDA',
        13,
        '2024-12-19 13:29:01',
        '2024-12-19 13:29:01',
        NULL,
        1,
        1,
        NULL
    ),
    (
        116,
        ' ',
        13,
        '2024-12-19 13:29:01',
        '2024-12-19 13:31:50',
        '2024-12-19 13:31:50',
        1,
        1,
        1
    ),
    (
        117,
        'BMW',
        13,
        '2024-12-19 13:29:01',
        '2024-12-19 13:29:01',
        NULL,
        1,
        1,
        NULL
    ),
    (
        118,
        'ABe',
        13,
        '2024-12-19 13:31:41',
        '2024-12-23 08:10:52',
        '2024-12-23 08:10:52',
        1,
        1,
        1
    ),
    (
        119,
        'QS',
        13,
        '2024-12-19 13:31:41',
        '2024-12-19 13:39:32',
        '2024-12-19 13:39:32',
        1,
        1,
        1
    ),
    (
        120,
        'QSR,bf,bf',
        13,
        '2024-12-19 13:31:41',
        '2024-12-19 13:39:24',
        '2024-12-19 13:39:24',
        1,
        1,
        1
    ),
    (
        121,
        'TEST 12378787',
        13,
        '2024-12-19 13:36:23',
        '2024-12-19 13:39:19',
        '2024-12-19 13:39:19',
        1,
        1,
        1
    ),
    (
        122,
        'QSR',
        13,
        '2024-12-19 13:36:44',
        '2024-12-19 13:39:28',
        '2024-12-19 13:39:28',
        1,
        1,
        1
    ),
    (
        123,
        'TEST 123',
        13,
        '2024-12-19 13:36:58',
        '2024-12-19 13:39:05',
        '2024-12-19 13:39:05',
        1,
        1,
        1
    ),
    (
        124,
        'TESTA 1237',
        13,
        '2024-12-19 13:37:04',
        '2024-12-19 13:39:01',
        '2024-12-19 13:39:01',
        1,
        1,
        1
    ),
    (
        125,
        'Dîner sur place',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        126,
        'à emporter',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        127,
        'livraison',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        128,
        'Wifi disponible',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        129,
        'Réservation obligatoire',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        130,
        'Parking',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        131,
        'organisation d\'événement',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        132,
        'payement par carte bancaire',
        24,
        '2024-12-23 08:07:57',
        '2024-12-23 08:07:57',
        NULL,
        1,
        1,
        NULL
    ),
    (
        133,
        'Test 123',
        20,
        '2024-12-23 13:03:07',
        '2024-12-23 13:04:23',
        '2024-12-23 13:04:23',
        1,
        1,
        1
    ),
    (
        134,
        'Eq vie noc',
        21,
        '2024-12-23 13:03:35',
        '2024-12-23 13:04:19',
        '2024-12-23 13:04:19',
        1,
        1,
        1
    ),
    (
        135,
        'test',
        12,
        '2024-12-26 12:41:51',
        '2024-12-26 12:41:57',
        '2024-12-26 12:41:57',
        1,
        1,
        1
    ),
    (
        136,
        'Manuelle',
        11,
        '2024-12-26 13:45:42',
        '2024-12-26 13:45:42',
        NULL,
        1,
        1,
        NULL
    ),
    (
        137,
        'type1',
        8,
        '2024-12-26 13:47:14',
        '2024-12-30 19:33:15',
        '2024-12-30 19:33:15',
        1,
        1,
        1
    ),
    (
        138,
        'type2',
        8,
        '2024-12-26 13:47:49',
        '2024-12-30 19:33:11',
        '2024-12-30 19:33:11',
        1,
        1,
        1
    ),
    (
        139,
        'type3',
        8,
        '2024-12-26 13:47:49',
        '2024-12-30 19:33:07',
        '2024-12-30 19:33:07',
        1,
        1,
        1
    ),
    (
        140,
        'moteur1',
        18,
        '2024-12-26 13:55:28',
        '2024-12-30 19:33:03',
        '2024-12-30 19:33:03',
        1,
        1,
        1
    ),
    (
        141,
        'Coca-cola',
        10,
        '2025-01-25 17:36:26',
        '2025-01-25 17:36:26',
        NULL,
        21,
        21,
        NULL
    ),
    (
        142,
        'QWQ',
        14,
        '2025-02-03 19:32:44',
        '2025-02-03 19:36:17',
        '2025-02-03 19:36:17',
        1,
        1,
        1
    ),
    (
        143,
        'ty1',
        18,
        '2025-02-03 19:35:55',
        '2025-02-03 19:36:12',
        '2025-02-03 19:36:12',
        1,
        1,
        1
    ),
    (
        144,
        't2',
        18,
        '2025-02-03 19:35:56',
        '2025-02-03 19:36:08',
        '2025-02-03 19:36:08',
        1,
        1,
        1
    ),
    (
        145,
        't1',
        19,
        '2025-02-03 19:37:37',
        '2025-02-03 19:38:37',
        '2025-02-03 19:38:37',
        1,
        1,
        1
    ),
    (
        146,
        't2',
        19,
        '2025-02-03 19:37:37',
        '2025-02-03 19:38:33',
        '2025-02-03 19:38:33',
        1,
        1,
        1
    ),
    (
        147,
        'e1',
        21,
        '2025-02-03 19:37:55',
        '2025-02-03 19:38:29',
        '2025-02-03 19:38:29',
        1,
        1,
        1
    ),
    (
        148,
        'qw',
        16,
        '2025-02-03 19:39:10',
        '2025-02-03 19:40:04',
        '2025-02-03 19:40:04',
        1,
        1,
        1
    ),
    (
        149,
        'qwqw',
        14,
        '2025-02-03 19:39:28',
        '2025-02-03 19:40:02',
        '2025-02-03 19:40:02',
        1,
        1,
        1
    ),
    (
        150,
        'p1',
        17,
        '2025-02-03 19:40:20',
        '2025-02-03 19:41:22',
        '2025-02-03 19:41:22',
        1,
        1,
        1
    ),
    (
        151,
        'e1',
        14,
        '2025-02-03 19:40:33',
        '2025-02-03 19:41:19',
        '2025-02-03 19:41:19',
        1,
        1,
        1
    ),
    (
        152,
        'tr',
        15,
        '2025-02-03 19:40:51',
        '2025-02-03 19:41:15',
        '2025-02-03 19:41:15',
        1,
        1,
        1
    ),
    (
        153,
        'qw',
        21,
        '2025-02-03 19:41:45',
        '2025-02-03 19:42:17',
        '2025-02-03 19:42:17',
        1,
        1,
        1
    ),
    (
        154,
        'crer',
        20,
        '2025-02-03 19:42:00',
        '2025-02-03 19:42:13',
        '2025-02-03 19:42:13',
        1,
        1,
        1
    ),
    (
        155,
        'Essence',
        18,
        '2025-02-06 19:31:24',
        '2025-02-06 19:31:24',
        NULL,
        1,
        1,
        NULL
    ),
    (
        156,
        'Gas-oil',
        18,
        '2025-02-06 19:31:24',
        '2025-02-06 19:31:24',
        NULL,
        1,
        1,
        NULL
    ),
    (
        157,
        'Hybride',
        18,
        '2025-02-06 19:31:24',
        '2025-02-06 19:31:24',
        NULL,
        1,
        1,
        NULL
    ),
    (
        158,
        'Équipements restauration 1',
        14,
        '2025-02-16 06:37:04',
        '2025-02-16 06:37:04',
        NULL,
        1,
        1,
        NULL
    ),
    (
        159,
        'Automatique',
        11,
        '2025-05-13 21:09:54',
        '2025-05-13 21:09:54',
        NULL,
        1,
        1,
        NULL
    ),
    (
        160,
        'Slow',
        19,
        '2025-05-16 09:04:07',
        '2025-05-16 09:04:07',
        NULL,
        1,
        1,
        NULL
    ),
    (
        161,
        'Climatisé',
        21,
        '2025-05-16 09:04:45',
        '2025-05-16 09:04:45',
        NULL,
        1,
        1,
        NULL
    ),
    (
        162,
        'restaurant',
        21,
        '2025-05-16 09:04:45',
        '2025-05-16 09:04:45',
        NULL,
        1,
        1,
        NULL
    ),
    (
        163,
        'TEST',
        20,
        '2025-05-16 09:05:05',
        '2025-05-16 09:05:05',
        NULL,
        1,
        1,
        NULL
    );