//
// bootstrap-chosen-variables.less
//
// An alternate stylesheet for <PERSON><PERSON> (http://harvesthq.github.com/chosen/).
// This one is supposed to integrate better with Bootstrap.
//
// Submit bugfixes to: http://github.com/alxlit/bootstrap-chosen
//

@chosen-background: @input-bg;
@chosen-border: 1px solid @input-border;
@chosen-border-radius: @input-border-radius;
@chosen-multi-border-radius: @chosen-border-radius;
@chosen-box-shadow: ~"inset 0 1px 1px rgba(0, 0, 0, .075)";
@chosen-drop-border: @input-border;
@chosen-drop-box-shadow: ~"0 8px 8px rgba(0, 0, 0, .25)";
@chosen-drop-zindex: 1060;
@chosen-focus-border: 1px solid @input-border-focus;
@chosen-focus-box-shadow: ~"0 1px 1px rgba(0, 0, 0, .075) inset, 0 0 8px rgba(82, 168, 236, .6)";
@chosen-focus-transition: ~"border linear .2s, box-shadow linear .2s";
@chosen-height: @input-height-base;
@chosen-multi-height: @input-height-base + 6px;
@chosen-sprite-path: "chosen-sprite.png";
@chosen-sprite-retina-path: "<EMAIL>";
